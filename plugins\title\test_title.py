import argparse
from . import TitleProcessor
from config import DOC_FORMAT

def main():
    """测试标题处理器的主函数
    
    功能：
    - 从命令行读取输入输出文件路径
    - 使用TitleProcessor处理文档
    - 保存处理后的文档
    
    参数：通过命令行参数传递
    - input: 输入文件路径(默认input.docx)
    - output: 输出文件路径(默认output.docx)
    
    返回值：无
    """
    parser = argparse.ArgumentParser(description="提取标题并写入注释的 Word 文档处理器")

    # 支持指定或使用默认路径
    parser.add_argument("input", nargs="?", default="input.docx", help="输入 Word 文件路径，默认 input.docx")
    parser.add_argument("output", nargs="?", default="output.docx", help="输出 Word 文件路径，默认 output.docx")

    args = parser.parse_args()

    title_processor = TitleProcessor(DOC_FORMAT)

    with open(args.input, "rb") as file:
        word_bytes = file.read()

    output_docx_bytes = title_processor.process(word_bytes)

    with open(args.output, "wb") as f:
        f.write(output_docx_bytes)

if __name__ == '__main__':
    main()
