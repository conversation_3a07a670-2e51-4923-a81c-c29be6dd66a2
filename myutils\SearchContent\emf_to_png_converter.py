"""
EMF到PNG高质量转换工具

该模块提供了将EMF（Enhanced Metafile）图片转换为高质量PNG图片的功能。
支持多种转换策略以确保输出图片的清晰度和质量。

作者: Assistant
创建时间: 2025-06-26
"""

import os
import tempfile
from io import BytesIO
from PIL import Image, ImageEnhance
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def convert_emf_to_high_quality_png(emf_data, output_path=None, dpi=600, enhance_quality=True):
    """
    将EMF图像数据转换为高质量PNG文件

    参数:
        emf_data (bytes): EMF图像的二进制数据
        output_path (str, optional): 输出PNG文件路径。如果为None，将返回PIL Image对象
        dpi (int): 输出分辨率，默认600 DPI（推荐300-1200）
        enhance_quality (bool): 是否启用图像质量增强

    返回:
        str or PIL.Image: 如果指定output_path则返回文件路径，否则返回PIL Image对象

    异常:
        ValueError: 输入数据无效
        IOError: 图像处理失败
    """
    if not emf_data:
        raise ValueError("EMF数据不能为空")

    try:
        # 从二进制数据创建图像对象
        with Image.open(BytesIO(emf_data)) as emf_image:
            logger.info(f"原始EMF图像尺寸: {emf_image.size}, 模式: {emf_image.mode}")

            # 计算目标尺寸
            target_width, target_height = _calculate_target_size(emf_image, dpi)
            logger.info(f"初始目标尺寸: {target_width}x{target_height}")

            # 检查高度和宽度是否超过1200像素，如果超过则降低DPI
            original_dpi = dpi
            max_dimension = 1200
            min_dpi = 150

            # 检查是否需要调整DPI（高度或宽度超过限制）
            if target_height > max_dimension or target_width > max_dimension:
                # 计算需要的DPI来将较大的维度控制在1200像素以下
                # 选择高度和宽度中较大的那个作为调整基准
                max_current_dimension = max(target_height, target_width)

                # 使用比例关系：new_dpi / original_dpi = max_dimension / max_current_dimension
                adjusted_dpi = int(dpi * max_dimension / max_current_dimension)
                adjusted_dpi = max(adjusted_dpi, min_dpi)  # 确保不低于最低DPI

                # 重新计算目标尺寸
                target_width, target_height = _calculate_target_size(emf_image, adjusted_dpi)
                dpi = adjusted_dpi  # 更新DPI变量

                logger.info(f"调整DPI从{original_dpi}到{dpi}，新目标尺寸: {target_width}x{target_height}")

                # 如果调整后仍然有维度超过最大限制，直接按比例缩放
                if target_height > max_dimension or target_width > max_dimension:
                    max_current_dimension = max(target_height, target_width)
                    scale_factor = max_dimension / max_current_dimension
                    target_width = int(target_width * scale_factor)
                    target_height = int(target_height * scale_factor)
                    logger.info(f"进一步缩放，最终尺寸: {target_width}x{target_height}")

            logger.info(f"最终目标尺寸: {target_width}x{target_height}")

            # 高质量重采样
            high_res_image = emf_image.resize(
                (target_width, target_height),
                resample=Image.LANCZOS
            )
            
            # 处理透明背景
            processed_image = _handle_transparency(high_res_image)
            
            # 图像质量增强
            if enhance_quality:
                processed_image = _enhance_image_quality(processed_image)
            
            # 保存或返回图像
            if output_path:
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else '.', exist_ok=True)

                # 保存为高质量PNG，使用调整后的DPI
                processed_image.save(
                    output_path,
                    "PNG",
                    dpi=(dpi, dpi),
                    compress_level=1,  # 低压缩以保持质量
                    optimize=True
                )
                logger.info(f"PNG文件已保存至: {output_path}，最终DPI: {dpi}")
                import cv2
                image = cv2.imread(output_path)
                image = cv2.copyMakeBorder(image, 50, 50, 50, 50, cv2.BORDER_CONSTANT, value=[255,255,255])#填充边界
                cv2.imwrite(output_path, image)

                return output_path
            else:
                return processed_image
                
    except Exception as e:
        logger.error(f"EMF转PNG失败: {str(e)}")
        raise IOError(f"图像转换失败: {str(e)}")


def _calculate_target_size(emf_image, target_dpi):
    """
    基于物理尺寸计算目标像素尺寸
    
    参数:
        emf_image: PIL Image对象
        target_dpi: 目标DPI
    
    返回:
        tuple: (目标宽度, 目标高度)
    """
    if "dpi" in emf_image.info:
        x_dpi, y_dpi = emf_image.info["dpi"]
        # 计算物理尺寸（英寸）
        phys_width_inch = emf_image.width / x_dpi
        phys_height_inch = emf_image.height / y_dpi
        
        # 按目标DPI计算像素尺寸
        target_width = int(phys_width_inch * target_dpi)
        target_height = int(phys_height_inch * target_dpi)
        
        # 修正非均匀DPI导致的宽高比失真
        if x_dpi != y_dpi:
            aspect_ratio = x_dpi / y_dpi
            target_height = int(target_height * aspect_ratio)
    else:
        # 无DPI信息时，按比例缩放
        scale_factor = target_dpi / 96  # 假设默认96dpi
        target_width = int(emf_image.width * scale_factor)
        target_height = int(emf_image.height * scale_factor)
    
    # 确保最小尺寸
    target_width = max(target_width, 100)
    target_height = max(target_height, 100)
    
    return target_width, target_height


def _handle_transparency(image):
    """
    处理图像透明背景，转换为白色背景
    
    参数:
        image: PIL Image对象
    
    返回:
        PIL.Image: 处理后的RGB图像
    """
    if image.mode in ('RGBA', 'LA'):
        # 创建白色背景
        background = Image.new("RGB", image.size, (255, 255, 255))
        
        # 合成图像
        if image.mode == 'RGBA':
            background.paste(image, mask=image.split()[3])  # 使用alpha通道作为mask
        else:  # LA模式
            background.paste(image, mask=image.split()[1])
        
        return background
    elif image.mode == 'P':
        # 调色板模式转换
        return image.convert('RGB')
    else:
        # 确保为RGB模式
        return image.convert('RGB')


def _enhance_image_quality(image):
    """
    增强图像质量
    
    参数:
        image: PIL Image对象
    
    返回:
        PIL.Image: 增强后的图像
    """
    # 轻微锐化
    enhancer = ImageEnhance.Sharpness(image)
    image = enhancer.enhance(1.1)
    
    # 轻微对比度增强
    enhancer = ImageEnhance.Contrast(image)
    image = enhancer.enhance(1.05)
    
    return image


def batch_convert_emf_to_png(emf_files_dict, output_dir, dpi=600, enhance_quality=True):
    """
    批量转换EMF文件到PNG
    
    参数:
        emf_files_dict (dict): {文件名: EMF二进制数据} 的字典
        output_dir (str): 输出目录
        dpi (int): 输出分辨率
        enhance_quality (bool): 是否启用质量增强
    
    返回:
        dict: {原文件名: PNG文件路径} 的转换结果字典
    """
    os.makedirs(output_dir, exist_ok=True)
    results = {}
    
    for filename, emf_data in emf_files_dict.items():
        try:
            # 生成PNG文件名
            base_name = os.path.splitext(filename)[0]
            png_filename = f"{base_name}.png"
            png_path = os.path.join(output_dir, png_filename)
            
            # 转换
            convert_emf_to_high_quality_png(
                emf_data, 
                png_path, 
                dpi=dpi, 
                enhance_quality=enhance_quality
            )
            
            results[filename] = png_path
            logger.info(f"成功转换: {filename} -> {png_filename}")
            
        except Exception as e:
            logger.error(f"转换失败 {filename}: {str(e)}")
            results[filename] = None
    
    return results


def convert_emf_file_to_png(emf_file_path, png_file_path=None, dpi=600, enhance_quality=True):
    """
    从文件路径转换EMF到PNG
    
    参数:
        emf_file_path (str): EMF文件路径
        png_file_path (str, optional): PNG输出路径，默认为同目录同名.png文件
        dpi (int): 输出分辨率
        enhance_quality (bool): 是否启用质量增强
    
    返回:
        str: PNG文件路径
    """
    if not os.path.exists(emf_file_path):
        raise FileNotFoundError(f"EMF文件不存在: {emf_file_path}")
    
    # 读取EMF文件
    with open(emf_file_path, 'rb') as f:
        emf_data = f.read()
    
    # 生成输出路径
    if png_file_path is None:
        base_name = os.path.splitext(emf_file_path)[0]
        png_file_path = f"{base_name}.png"
    
    # 转换
    return convert_emf_to_high_quality_png(
        emf_data, 
        png_file_path, 
        dpi=dpi, 
        enhance_quality=enhance_quality
    )

import os
import tempfile
from PIL import Image

def convert_emf_to_highres_png(img_data, output_dir, filename, dpi=300):
    """
    将EMF/WMF格式图像数据转换为高分辨率PNG
    
    参数:
        img_data (bytes): 二进制图像数据 (EMF/WMF格式)
        output_dir (str): 输出目录路径
        filename_base (str): 输出文件名（不含扩展名）
        dpi (int): 输出分辨率（默认300 DPI）
    
    返回:
        str: 生成的PNG文件路径
    
    异常:
        ValueError: 输入数据为空时抛出
        IOError: 图像处理失败时抛出
    """
    # 验证输入数据
    if not img_data:
        raise ValueError("输入图像数据为空")
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建临时文件路径（使用安全随机文件名）
    with tempfile.NamedTemporaryFile(suffix='.emf', delete=False, dir=output_dir) as temp_file:
        temp_path = temp_file.name
    
    try:
        # 写入临时EMF文件
        with open(temp_path, 'wb') as f:
            f.write(img_data)
        
        filename_base = os.path.splitext(filename)[0]
        # 设置输出PNG路径
        png_path =  f"{filename_base}.png"
        
        # 转换并保存为高分辨率PNG
        with Image.open(temp_path) as img:
            # 高分辨率加载（支持矢量图元文件）
            img.load(dpi=dpi)
            
            # 保存为PNG格式（保留元数据）
            img.save(png_path, 'PNG', dpi=(dpi, dpi))
        
        return png_path
    
    except Exception as e:
        # 清理临时文件（如果转换失败）
        if os.path.exists(temp_path):
            os.remove(temp_path)
        raise IOError(f"图像转换失败: {str(e)}")
    
    finally:
        # 确保删除临时文件
        if os.path.exists(temp_path):
            os.remove(temp_path)

import os
from io import BytesIO
from PIL import Image

def convert_emf_to_png(img_data, output_dir, filename, dpi=1000):
    """
    将EMF图像数据转换为高分辨率PNG文件
    
    参数:
        img_data: EMF图像的二进制数据
        output_dir: PNG文件的输出目录
        filename: 输出文件名（不含扩展名）
        dpi: 输出分辨率（默认300 DPI）
    
    返回:
        输出PNG文件的完整路径
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 从二进制数据创建图像对象
    emf_image = Image.open(BytesIO(img_data))
    
    # 修正非均匀DPI导致的宽高比失真[8](@ref)
    if "dpi" in emf_image.info and emf_image.info["dpi"][0] != emf_image.info["dpi"][1]:
        x_dpi, y_dpi = emf_image.info["dpi"]
        aspect_ratio = x_dpi / y_dpi
        new_size = (emf_image.width, int(emf_image.height * aspect_ratio))
        emf_image = emf_image.resize(new_size, Image.LANCZOS)
    
    # 创建白色背景并合并图像（解决透明背景问题）[1,2](@ref)
    if emf_image.mode in ('RGBA', 'LA'):
        bg = Image.new("RGB", emf_image.size, (255, 255, 255))
        bg.paste(emf_image, mask=emf_image.split()[3])
        processed_image = bg
    else:
        processed_image = emf_image
    
    # 构建输出路径
    png_filename = f"{os.path.splitext(filename)[0]}.png"
    output_path = png_filename
    
    # 保存为高分辨率PNG
    processed_image.save(output_path, format="PNG", dpi=(dpi, dpi))
    
    return output_path

def convert_emf_to_png2(img_data, output_dir, filename, dpi=300):
    """
    优化版EMF转PNG函数，通过物理尺寸计算和高质量重采样提升清晰度
    
    参数:
        img_data: EMF图像的二进制数据
        output_dir: PNG文件的输出目录
        filename: 输出文件名（不含扩展名）
        dpi: 输出分辨率（默认300 DPI）
    
    返回:
        输出PNG文件的完整路径
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # 从二进制数据加载EMF并自动关闭资源
    with Image.open(BytesIO(img_data)) as emf_image:
        # 关键改进1：基于物理尺寸计算目标像素尺寸（保留矢量精度）
        if "dpi" in emf_image.info:
            x_dpi, y_dpi = emf_image.info["dpi"]
            # 计算物理尺寸（英寸）
            phys_width_inch = emf_image.width / x_dpi
            phys_height_inch = emf_image.height / y_dpi
            # 按目标DPI转换物理尺寸为像素[6,8](@ref)
            target_width = int(phys_width_inch * dpi)
            target_height = int(phys_height_inch * dpi)
        else:
            # 若无DPI元数据，则按比例缩放至目标DPI基准
            scale_factor = dpi / 72  # 假设默认72dpi
            target_width = int(emf_image.width * scale_factor)
            target_height = int(emf_image.height * scale_factor)
        
        # 关键改进2：使用LANCZOS重采样（抗锯齿保真）[7,8](@ref)
        resized_img = emf_image.resize(
            (target_width, target_height), 
            resample=Image.LANCZOS
        )
        
        # 关键改进3：RGBA模式下的白色背景合成（避免透明失真）
        if resized_img.mode == 'RGBA':
            bg = Image.new("RGB", resized_img.size, (255, 255, 255))
            bg.paste(resized_img, mask=resized_img.split()[3])
            processed_image = bg
        else:
            processed_image = resized_img.convert("RGB")
        
        # 构建输出路径并保存
        png_path = f"{os.path.splitext(filename)[0]}.png"
        processed_image.save(png_path, "PNG", dpi=(dpi, dpi), compress_level=9)
        
    return png_path



# 示例使用
if __name__ == "__main__":
    # 示例1: 从文件转换
    try:
        emf_path = "input/input.emf"  # 替换为实际EMF文件路径
        png_path = convert_emf_file_to_png(emf_path, dpi=300)
        print(f"转换完成: {png_path}")
        emf_path = "input/input2.emf"  # 替换为实际EMF文件路径
        png_path = convert_emf_file_to_png(emf_path, dpi=250)
        print(f"转换完成: {png_path}")
    except FileNotFoundError:
        print("示例EMF文件不存在，跳过文件转换示例")
    
    # 示例2: 从二进制数据转换
    # emf_data = b"..."  # 实际的EMF二进制数据
    # png_path = convert_emf_to_high_quality_png(emf_data, "output.png", dpi=600)
    # print(f"从二进制数据转换完成: {png_path}")
