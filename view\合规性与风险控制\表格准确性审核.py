import streamlit as st
from shared.audit_page import AuditPage
from plugins.tabulate import TabulateProcessor

def init_tabulate_processor():
    """初始化表格准确性处理器
    
    功能：
    - 设置表格准确性审查界面
    - 准备表格审查所需参数
    
    参数：无
    返回值：无
    """
    st.write(f"使用默认参数进行自动审查")
    return TabulateProcessor()

if __name__ == "__main__":
    page = AuditPage(init_tabulate_processor,'表格准确性审核')
    page.render()
