from docx import Document
from io import BytesIO
from . import FirmProcessor

if __name__ == "__main__":
    # 构造测试文档
    d = Document()
    d.add_paragraph("本协议由北京中科有限公司签署，")
    d.add_paragraph("后续文中提及为中科股份有限公司、北京中科有限责任公司等。")
    d.add_paragraph("请确保公司名称的一致性。")

    # 保存为字节流
    buf = BytesIO()
    d.save(buf)
    buf.seek(0)

    # 初始化并处理
    processor = FirmProcessor()
    result = processor.process(buf.getvalue())

    # 输出到本地文件查看结果
    with open("temp/firm.docx", "wb") as f:
        f.write(result)

    print("已完成公司名称一致性检查。")