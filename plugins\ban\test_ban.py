from . import BanProcessor

if __name__ == "__main__":
    from docx import Document
    from io import BytesIO

    # 构建测试文档
    doc = Document()
    doc.add_paragraph("这是一段含有火药的文字，还有炸药。")
    doc.add_paragraph("二甲苯是一个白名单词，应该被匹配但不批注。")
    doc.add_paragraph("炸药和药都在这里，应该只匹配炸药。")
    
    # 保存为 bytes
    buffer = BytesIO()
    doc.save(buffer)
    buffer.seek(0)

    # 初始化处理器（加入白名单词）
    processor = BanProcessor(
        blacklist=["火药", "炸药", "药", "甲苯", "二甲苯"],
        whitelist=["二甲苯"]  # 会被匹配，但不会批注
    )

    # 处理文档
    output_bytes = processor.process(buffer.getvalue())

    # 保存结果
    with open("temp/ban.docx", "wb") as f:
        f.write(output_bytes)

    print("测试完成：文档已处理并保存为 'banned_result.docx'")
