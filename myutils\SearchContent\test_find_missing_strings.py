
#arr2 = ['水泥', '石英砂', '天然砂/中砂/机制砂', '陶粒/石子/再生集料', '水', '颜料']
#arr1 = ['水泥、石英砂、天然砂', '中砂/机制砂、陶粒、石', '子、再生集料', 'G1-1装卸粉尘、', '原料储备', 'G1-2商仓粉尘', '水', '配料、搅拌', 'G1-3投料粉尘', '二', '水', '制品成型', 'T', '电供热', '制品养护', '深加工', 'G1-4抛丸粉尘', '成品码垛', 'S1-1残渣', '成品']
    

import re

def extract_chinese(s):
    """提取字符串中的中文字符部分"""
    # 匹配连续的中文字符（Unicode范围：4E00-9FA5）
    return re.findall(r'[\u4e00-\u9fa5]+', s)

def process_array(arr):
    """处理字符串数组：分割并提取中文部分"""
    result = []
    for s in arr:
        # 使用顿号、斜杠和空格作为分隔符分割字符串
        parts = re.split(r'[、/ ]+', s)
        # 提取每个部分的中文字符并拼接
        for part in parts:
            chinese_parts = extract_chinese(part)
            if chinese_parts:
                # 拼接所有连续的中文字符
                result.append(''.join(chinese_parts))
    return result

def find_missing_strings(arr1, arr2):
    """查找arr1中有但arr2中没有的字符串"""
    # 处理arr2，创建查找集合
    arr2_set = set(process_array(arr2))
    
    # 处理arr1并记录关系
    arr11 = []
    relationship = []
    
    for s in arr1:
        parts = process_array([s])
        arr11.extend(parts)
        relationship.append({
            'original': s,
            'processed': parts
        })
    
    # 查找缺失的字符串
    missing = []
    for s in arr11:
        if s not in arr2_set:
            missing.append(s)
    
    return missing, relationship

import re

def filter_partial_matches(missing_list, arr2):
    """
    过滤缺失字符串列表，移除与arr2中元素存在部分匹配的项
    参数:
        missing_list: 待过滤的缺失字符串列表
        arr2: 参考字符串数组（支持包含分隔符的复合字符串）
    返回:
        过滤后的缺失字符串列表
    """
    missing_data=[];missing_flag=True
    for missing_word in missing_list:
        missing_indexs=[]
        for i,word_in_arr2 in enumerate(arr2):
            if missing_word in word_in_arr2:
                missing_indexs.append(i)       
        missing_data.append((missing_word,missing_indexs,missing_flag))

    #print("missing_data",missing_data)
    
    # 步骤2：找出所有冲突的索引（被多个缺失词共享的位置）
    conflict_indices = set()
    index_count = {}
    
    # 统计每个索引出现的次数
    for _, indices, _ in missing_data:
        for idx in indices:
            index_count[idx] = index_count.get(idx, 0) + 1
    
    # 标记出现冲突的索引（被≥2个词共享）
    for idx, count in index_count.items():
        if count > 1:
            conflict_indices.add(idx)
    
    # 步骤3：标记需要过滤的词（与冲突索引有关联的词）
    filtered_data = []
    for word, indices, flag in missing_data:
        # 检查当前词是否关联到任何冲突索引
        if any(idx in conflict_indices for idx in indices):
            flag = False  # 标记为需要过滤
        filtered_data.append((word, indices, flag))
    
    # 步骤4：收集最终保留的词（标志为True的词）
    result = [word for word, _, flag in filtered_data if flag]
    
    return result

import re

def final_filter_strings(original_arr):
    # 步骤1：去除含"原料"、"成品"的字符串
    keywords = ["原料","原材料"]
    filtered_arr = [s for s in original_arr if all(keyword not in s for keyword in keywords)]
    keywords=["成品","一", "二", "三", "四", "五", "六", "七", "八", "九","十","丰"]
    filtered_arr = [s for s in filtered_arr if all(s!=keyword for keyword in keywords)]

    return filtered_arr
    

arr2 = ['水泥', '石英砂', '天然砂/中砂/机制砂', '陶粒/石子/再生集料', '水', '颜料']
arr1 = ['水泥、石英砂、天然砂', '中砂/机制砂、陶粒、石', '子、再生集料', 'G1-1装卸粉尘、', '原料储备', 'G1-2商仓粉尘', '水', '配料、搅拌', 'G1-3投料粉尘', '二', '水', '制品成型', 'T', '电供热', '制品养护', '深加工', 'G1-4抛丸粉尘', '成品码垛', 'S1-1残渣', '成品']
    

# 执行查找
missing_strings, relationship = find_missing_strings(arr1, arr2)
print("缺失的字符串1:", missing_strings)


missing_strings=filter_partial_matches(missing_strings,arr2)
print("缺失的字符串2:", missing_strings)

missing_strings=final_filter_strings(missing_strings)
print("缺失的字符串3:", missing_strings)

print("\n数据结构关系:")
for item in relationship:
    print(f"原始: '{item['original']}'")
    print(f"处理: {item['processed']}")
    print()