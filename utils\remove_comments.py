import io
import zipfile
from lxml import etree

def remove_docx_comments(doc_bytes: bytes) -> bytes:
    """从DOCX文件中移除所有注释
    
    参数:
        doc_bytes: DOCX文件的字节内容
        
    返回值:
        bytes: 移除注释后的DOCX文件字节内容
    """
    ns = 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'

    # 解压 docx 文件为字典
    with zipfile.ZipFile(io.BytesIO(doc_bytes), 'r') as z:
        files = {name: z.read(name) for name in z.namelist()}

    # 处理 word/document.xml
    if 'word/document.xml' in files:
        root = etree.fromstring(files['word/document.xml'])
        for tag in ('commentRangeStart', 'commentRangeEnd', 'commentReference'):
            for elem in root.findall(f'.//{{{ns}}}{tag}'):
                parent = elem.getparent()
                if parent is not None:
                    parent.remove(elem)
        files['word/document.xml'] = etree.tostring(root, xml_declaration=True, encoding='UTF-8', standalone='yes')

    # 删除 word/comments.xml
    files.pop('word/comments.xml', None)

    # 移除注释关系
    rels_path = 'word/_rels/document.xml.rels'
    if rels_path in files:
        rels = etree.fromstring(files[rels_path])
        changed = False
        for rel in list(rels):
            if rel.get('Type', '').endswith('/comments'):
                rels.remove(rel)
                changed = True
        if changed:
            files[rels_path] = etree.tostring(rels, xml_declaration=True, encoding='UTF-8', standalone='yes')

    # 重打包为新的 docx
    out = io.BytesIO()
    with zipfile.ZipFile(out, 'w', zipfile.ZIP_DEFLATED) as z:
        for name, data in files.items():
            z.writestr(name, data)
    return out.getvalue()

if __name__ == '__main__':
    with open('merged.docx', 'rb') as f:
        doc = f.read()
    cleaned = remove_docx_comments(doc)
    with open('no_comments.docx', 'wb') as f:
        f.write(cleaned)
