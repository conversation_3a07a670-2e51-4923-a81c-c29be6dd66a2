from shared.custom_document import CustomDocument
from plugins.title.chapter_tree import ChapterTree
from .config import DOC_FORMAT

class TitleProcessor():
    """
    标题处理器类，用于处理文档中的标题结构一致性。
    """
    def __init__(self, yaml_str:str = DOC_FORMAT) -> None:
        """
        初始化 TitleProcessor 实例。

        参数:
            yaml_str (str): YAML 格式的字符串，包含标题结构定义，默认为 DOC_FORMAT。
        """
        self.yaml_str = yaml_str

    def __call__(self, doc_bytes: bytes) -> bytes:
        """处理文档标题结构一致性
        
        功能：
        - 检查文档标题结构是否符合预定义格式
        - 对不符合的标题添加批注说明
        
        参数:
            doc_bytes (bytes): 待处理的文档字节数据
            
        返回值:
            bytes: 处理后的文档字节数据
        """
        doc = CustomDocument(doc_bytes)
        tree = ChapterTree(self.yaml_str)
        tree.set_text(doc.get_marked_text())
        doc.insert_comment_at_position(
            [(val, '该段下缺少：'+key) for key, val in tree.get_failed_nodes().items()],
            author="结构一致性审核"
        )
        return doc.get_docx_bytes()
