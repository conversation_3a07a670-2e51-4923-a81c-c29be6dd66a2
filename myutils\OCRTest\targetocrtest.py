#!/usr/bin/env python3
"""
OCR文字识别模块
提供图像文字识别功能，返回文字内容和坐标信息
"""

import os
import logging
import numpy as np
from typing import List, Tuple, Union, Optional
from PIL import Image
import cv2

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    logger.warning("PaddleOCR未安装，将尝试使用其他OCR引擎")
    PADDLEOCR_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    logger.warning("EasyOCR未安装")
    EASYOCR_AVAILABLE = False

target_keywords=["下料","原料","原材料"]


class OCRResult:
    """OCR识别结果类"""

    def __init__(self, text: str, bbox: List[List[int]], confidence: float = 0.0):
        """
        初始化OCR结果

        Args:
            text: 识别的文字内容
            bbox: 文字边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
            confidence: 识别置信度
        """
        self.text = text
        self.bbox = bbox
        self.confidence = confidence

    def get_center(self) -> Tuple[int, int]:
        """获取文字区域中心点坐标"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        center_x = int(sum(x_coords) / len(x_coords))
        center_y = int(sum(y_coords) / len(y_coords))
        return center_x, center_y

    def get_text(self) -> str:
        """获取识别文字内容"""
        return self.text

    def get_rect_bbox(self) -> Tuple[int, int, int, int]:
        """获取矩形边界框 (x, y, width, height)"""
        x_coords = [point[0] for point in self.bbox]
        y_coords = [point[1] for point in self.bbox]
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)
        return x_min, y_min, x_max - x_min, y_max - y_min

    def __str__(self):
        center = self.get_center()
        return f"OCRResult(text='{self.text}', center={center}, confidence={self.confidence:.3f})"


def ocr_recognize_text(image_input: Union[str, np.ndarray, Image.Image],
                      engine: str = 'paddleocr',
                      language: str = 'ch',
                      confidence_threshold: float = 0.5) -> List[OCRResult]:
    """
    对图像进行文字识别，返回文字内容和坐标信息

    Args:
        image_input: 输入图像，可以是文件路径、numpy数组或PIL Image对象
        engine: OCR引擎选择 ('paddleocr', 'easyocr')
        language: 识别语言 ('ch'=中文, 'en'=英文, 'ch_en'=中英文混合)
        confidence_threshold: 置信度阈值，低于此值的结果将被过滤

    Returns:
        List[OCRResult]: OCR识别结果列表

    Raises:
        ValueError: 输入参数无效
        RuntimeError: OCR引擎不可用或识别失败
    """

    # 验证输入
    if image_input is None:
        raise ValueError("图像输入不能为空")

    # 预处理图像
    image_array = _preprocess_image(image_input)

    # 根据引擎选择进行OCR识别
    if engine.lower() == 'paddleocr':
        return _paddleocr_recognize(image_array, language, confidence_threshold)
    elif engine.lower() == 'easyocr':
        return _easyocr_recognize(image_array, language, confidence_threshold)
    else:
        raise ValueError(f"不支持的OCR引擎: {engine}")
def _transparent_to_white(img, output_path=None):
    # 读取带透明通道的图像（RGBA格式）
    #img = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
    
    # 分离通道
    if img.shape[2] == 4:  # 检查是否有alpha通道
        b, g, r, a = cv2.split(img)
        
        # 创建白色背景
        white_bg = np.ones_like(b) * 255
        
        # 使用alpha通道作为掩码混合图像
        b = b * (a/255) + white_bg * (1 - a/255)
        g = g * (a/255) + white_bg * (1 - a/255)
        r = r * (a/255) + white_bg * (1 - a/255)
        
        # 合并通道（去掉alpha通道）
        result = cv2.merge([b, g, r])
    else:
        result = img  # 如果没有透明通道，直接返回原图
    
    # 保存结果
    if output_path is not None:
        
        cv2.imwrite(output_path, result)
    return result

def _preprocess_image(image_input: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
    """
    预处理图像，转换为numpy数组格式

    Args:
        image_input: 输入图像

    Returns:
        np.ndarray: 处理后的图像数组
    """
    try:
        if isinstance(image_input, str):
            # 文件路径
            if not os.path.exists(image_input):
                raise FileNotFoundError(f"图像文件不存在: {image_input}")
            image = cv2.imread(image_input)
            image = cv2.copyMakeBorder(image, 50, 50, 50, 50, cv2.BORDER_CONSTANT, value=[255,255,255])#填充边界
            if image is None:
                raise ValueError(f"无法读取图像文件: {image_input}")
            # OpenCV读取的是BGR格式，转换为RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            image = _transparent_to_white(image)

        elif isinstance(image_input, np.ndarray):
            # numpy数组
            image = image_input.copy()

        elif isinstance(image_input, Image.Image):
            # PIL Image对象
            image = np.array(image_input)

        else:
            raise ValueError(f"不支持的图像输入类型: {type(image_input)}")

        # 确保图像是3通道RGB格式
        if len(image.shape) == 2:
            # 灰度图转RGB
            image = cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        elif len(image.shape) == 3 and image.shape[2] == 4:
            # RGBA转RGB
            image = cv2.cvtColor(image, cv2.COLOR_RGBA2RGB)

        logger.info(f"图像预处理完成，尺寸: {image.shape}")
        return image

    except Exception as e:
        logger.error(f"图像预处理失败: {str(e)}")
        raise ValueError(f"图像预处理失败: {str(e)}")


def _check_gpu_available() -> bool:
    """检查GPU是否可用"""
    try:
        import paddle
        return paddle.is_compiled_with_cuda()
    except:
        return False

def _paddleocr_recognize(image: np.ndarray,
                        language: str,
                        confidence_threshold: float) -> List[OCRResult]:
    """
    使用PaddleOCR进行文字识别

    Args:
        image: 图像数组
        language: 识别语言
        confidence_threshold: 置信度阈值

    Returns:
        List[OCRResult]: 识别结果列表
    """
    if not PADDLEOCR_AVAILABLE:
        raise RuntimeError("PaddleOCR未安装，请安装: pip install paddleocr")

    try:
        # 初始化PaddleOCR（简化版本）
        lang_map = {
            'ch': 'ch',
            'en': 'en',
            'ch_en': 'ch'
        }
        ocr_lang = lang_map.get(language, 'ch')
    
        # 创建OCR实例 - 使用更宽松的检测参数
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang=ocr_lang,
            det_db_thresh=0.1,
            det_db_box_thresh=0.3,
            det_db_unclip_ratio=2.0,
            
        )

        cv2.imwrite("output/debug_preprocessed.png", image)

        # 进行OCR识别
        logger.info("开始PaddleOCR文字识别...")
        try:
            # 尝试使用新的方法（不带cls参数）
            results = ocr.predict(image)
        except (AttributeError, TypeError):
            try:
                # 如果新方法不存在或参数错误，尝试旧方法
                results = ocr.ocr(image, cls=True)
            except:
                # 最后尝试不带cls参数的旧方法
                results = ocr.ocr(image)

        #print(results)

        # 解析结果 - 处理新版PaddleOCR的返回格式
        ocr_results = []
        total_detected = 0
        
        if results:
             # 检查是否是新版本的字典格式返回
             if isinstance(results, list) and len(results) > 0 and isinstance(results[0], dict):
                 # 新版本返回格式: 打印所有键来调试
                 result_dict = results[0]
                 print(f"字典键: {list(result_dict.keys())}")
                 
                 # 尝试不同的可能键名
                 possible_text_keys = ['rec_texts', 'rec_text', 'texts', 'text']
                 possible_score_keys = ['rec_scores', 'rec_score', 'scores', 'score', 'confidences']
                 possible_box_keys = ['rec_polys', 'rec_boxes', 'det_boxes', 'boxes', 'box', 'det_box', 'bboxes']
                 
                 rec_texts = []
                 rec_scores = []
                 det_boxes = []
                 
                 for key in possible_text_keys:
                     if key in result_dict:
                         rec_texts = result_dict[key]
                         print(f"找到文字键: {key}, 长度: {len(rec_texts)}")
                         break
                         
                 for key in possible_score_keys:
                     if key in result_dict:
                         rec_scores = result_dict[key]
                         print(f"找到置信度键: {key}, 长度: {len(rec_scores)}")
                         break
                         
                 for key in possible_box_keys:
                     if key in result_dict:
                         det_boxes = result_dict[key]
                         print(f"找到边界框键: {key}, 长度: {len(det_boxes)}")
                         break
                 
                 total_detected = len(rec_texts)
                 print(f"新版本格式 - 检测到 {total_detected} 个文字区域")
                 
                 for i in range(min(len(rec_texts), len(rec_scores), len(det_boxes))):
                     text = rec_texts[i]
                     confidence = float(rec_scores[i])
                     bbox = det_boxes[i]
                     
                     print(f"文字 {i+1}: '{text}', 置信度: {confidence:.3f}, 阈值: {confidence_threshold}")
                     
                     # 过滤低置信度结果
                     if confidence >= confidence_threshold:
                         # 转换坐标格式
                         if isinstance(bbox, np.ndarray):
                             bbox_int = bbox.tolist()
                         else:
                             bbox_int = [[int(point[0]), int(point[1])] for point in bbox]
                         ocr_results.append(OCRResult(text, bbox_int, confidence))
                         print(f"  -> 通过阈值，已添加到结果")
                     else:
                         print(f"  -> 未通过阈值 (需要 >= {confidence_threshold})")
                        
             elif isinstance(results, list) and len(results) > 0 and isinstance(results[0], list):
                # 旧版本返回格式: [[bbox, (text, confidence)], ...]
                for line in results[0]:
                    if line and len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文字, 置信度)
                        total_detected += 1

                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = float(text_info[1])
                            print(f"检测到文字: '{text}', 置信度: {confidence:.3f}")

                            # 过滤低置信度结果
                            if confidence >= confidence_threshold:
                                # 转换坐标格式
                                bbox_int = [[int(point[0]), int(point[1])] for point in bbox]
                                ocr_results.append(OCRResult(text, bbox_int, confidence))
        
        print(f"总共检测到 {total_detected} 个区域，通过置信度阈值 {confidence_threshold} 的有 {len(ocr_results)} 个")

        logger.info(f"PaddleOCR识别完成，共识别到 {len(ocr_results)} 个文字区域")
        return ocr_results

    except Exception as e:
        logger.error(f"PaddleOCR识别失败: {str(e)}")
        raise RuntimeError(f"PaddleOCR识别失败: {str(e)}")


def _easyocr_recognize(image: np.ndarray,
                      language: str,
                      confidence_threshold: float) -> List[OCRResult]:
    """
    使用EasyOCR进行文字识别

    Args:
        image: 图像数组
        language: 识别语言
        confidence_threshold: 置信度阈值

    Returns:
        List[OCRResult]: 识别结果列表
    """
    if not EASYOCR_AVAILABLE:
        raise RuntimeError("EasyOCR未安装，请安装: pip install easyocr")

    try:
        # 语言映射
        lang_map = {
            'ch': ['ch_sim', 'en'],
            'en': ['en'],
            'ch_en': ['ch_sim', 'en']
        }
        ocr_langs = lang_map.get(language, ['ch_sim', 'en'])

        # 创建EasyOCR实例
        reader = easyocr.Reader(ocr_langs, gpu=False)

        # 进行OCR识别
        logger.info("开始EasyOCR文字识别...")
        results = reader.readtext(image)

        # 解析结果
        ocr_results = []
        for result in results:
            if len(result) >= 3:
                bbox = result[0]  # 边界框坐标
                text = result[1]  # 文字内容
                confidence = float(result[2])  # 置信度

                # 过滤低置信度结果
                if confidence >= confidence_threshold:
                    # 转换坐标格式
                    bbox_int = [[int(point[0]), int(point[1])] for point in bbox]
                    ocr_results.append(OCRResult(text, bbox_int, confidence))

        logger.info(f"EasyOCR识别完成，共识别到 {len(ocr_results)} 个文字区域")
        return ocr_results

    except Exception as e:
        logger.error(f"EasyOCR识别失败: {str(e)}")
        raise RuntimeError(f"EasyOCR识别失败: {str(e)}")


def visualize_ocr_results(image_input: Union[str, np.ndarray, Image.Image],
                         ocr_results: List[OCRResult],
                         output_path: Optional[str] = None) -> np.ndarray:
    """
    可视化OCR识别结果，在图像上绘制文字框和文字内容

    Args:
        image_input: 输入图像
        ocr_results: OCR识别结果列表
        output_path: 输出图像路径（可选）

    Returns:
        np.ndarray: 绘制了识别结果的图像
    """
    # 预处理图像
    image = _preprocess_image(image_input)
    result_image = image.copy()

    # 绘制识别结果
    for result in ocr_results:
        # 绘制边界框
        bbox = np.array(result.bbox, dtype=np.int32)
        cv2.polylines(result_image, [bbox], True, (0, 255, 0), 2)

        # 绘制文字内容
        center_x, center_y = result.get_center()
        text = f"{result.text} ({result.confidence:.2f})"

        # 设置字体（使用OpenCV默认字体）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1

        # 获取文字尺寸
        (text_width, text_height), _ = cv2.getTextSize(text, font, font_scale, thickness)

        # 绘制文字背景
        cv2.rectangle(result_image,
                     (center_x - text_width//2 - 5, center_y - text_height - 5),
                     (center_x + text_width//2 + 5, center_y + 5),
                     (255, 255, 255), -1)

        # 绘制文字
        cv2.putText(result_image, text,
                   (center_x - text_width//2, center_y),
                   font, font_scale, (0, 0, 255), thickness)

    # 保存结果图像
    if output_path:
        # 转换为BGR格式保存
        save_image = cv2.cvtColor(result_image, cv2.COLOR_RGB2BGR)
        cv2.imwrite(output_path, save_image)
        logger.info(f"可视化结果已保存至: {output_path}")

    return result_image


def filter_ocr_results(ocr_results: List[OCRResult],
                      min_confidence: float = 0.7,
                      min_text_length: int = 1,
                      keywords: Optional[List[str]] = None) -> List[OCRResult]:
    """
    过滤OCR识别结果

    Args:
        ocr_results: OCR识别结果列表
        min_confidence: 最小置信度阈值
        min_text_length: 最小文字长度
        keywords: 关键词列表（如果指定，只保留包含关键词的结果）

    Returns:
        List[OCRResult]: 过滤后的结果列表
    """
    filtered_results = []

    for result in ocr_results:
        # 置信度过滤
        if result.confidence < min_confidence:
            continue

        # 文字长度过滤
        if len(result.text.strip()) < min_text_length:
            continue

        # 关键词过滤
        if keywords:
            if not any(keyword in result.text for keyword in keywords):
                continue

        filtered_results.append(result)

    return filtered_results


def extract_text_by_region(ocr_results: List[OCRResult],
                          region: Tuple[int, int, int, int]) -> List[OCRResult]:
    """
    提取指定区域内的文字

    Args:
        ocr_results: OCR识别结果列表
        region: 区域坐标 (x, y, width, height)

    Returns:
        List[OCRResult]: 区域内的文字结果
    """
    x, y, width, height = region
    region_results = []

    for result in ocr_results:
        center_x, center_y = result.get_center()

        # 检查中心点是否在指定区域内
        if (x <= center_x <= x + width and
            y <= center_y <= y + height):
            region_results.append(result)

    return region_results


def extract_text_around_target(ocr_results: List[OCRResult], target_texts: List[str]) -> Tuple[List[OCRResult], List[OCRResult]]:
    """
    提取目标文本周围的上方和左边文字
    
    Args:
        ocr_results: OCR识别结果列表
        target_texts: 目标文本列表，默认为["下料"]
        
    Returns:
        Tuple[List[OCRResult], List[OCRResult]]: 上方文字列表和左边文字列表
    """
    # 查找第一个匹配的目标文本
    target_result = None
    for result in ocr_results:
        for target_text in target_texts:
            if  target_text in result.text:
                target_result = result
                break

    if target_result is None:
        return [], []  # 没有找到目标

    # 获取目标区域中心点
    target_x, target_y = target_result.get_center()
    
    above_texts = []   # 上方的文本
    left_texts = []    # 左边的文本

    for result in ocr_results:
        if result == target_result:
            continue   # 跳过自身
            
        center_x, center_y = result.get_center()
        
        # 检查是否在上方
        if center_y < target_y and center_x-target_x<200:
            above_texts.append(result)
            
        # 检查是否在左边
        if center_x < target_x and target_x-center_x>200:
            left_texts.append(result)

    return above_texts, left_texts

def test_ocr_function(test_image_path):
    """
    测试OCR功能的示例函数
    """
    print("=== OCR文字识别测试 ===")
    
    # 测试图像路径
    #test_image_path = "input/2.png"
    
    # 检查测试图像是否存在
    if not os.path.exists(test_image_path):
        print(f"错误: 测试图像不存在: {test_image_path}")
        print("请确保在项目根目录下运行此脚本")
        print(f"当前工作目录: {os.getcwd()}")
        print("=== 测试终止 ===")
        return

    try:
        print(f"测试图像: {test_image_path}")
        
        
        print("\n[尝试] 使用置信度阈值=0.75")
        results = ocr_recognize_text(
            test_image_path,
            engine='paddleocr',
            language='ch_en',
            confidence_threshold=0.8
        )
        print(f"识别到 {len(results)} 个文字区域 (阈值=0.75)")

        filter_results=filter_ocr_results(results, min_confidence=0.75, min_text_length=1, keywords=target_keywords)

        # 显示结果详情
        if filter_results:
            print("\n识别结果详情:")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result}")
                print(f"   矩形边界框: {result.get_rect_bbox()}")
                print(f"   文字内容: {result.get_text()}")
                print(f"文字中心:{result.get_center()}")

            # 测试新功能：提取"下料"周围文本

           
            above_texts, left_texts = extract_text_around_target(results, target_keywords)

             # Remove '→' symbol from above_texts and left_texts while preserving the rest of the text
            above_texts = [OCRResult(text.text.replace('→', ''), text.bbox, text.confidence) if '→' in text.text else text for text in above_texts]
            left_texts = [OCRResult(text.text.replace('→', ''), text.bbox, text.confidence) if '→' in text.text else text for text in left_texts]
           

            extracted_texts=[]
            extracted_texts.extend(above_texts)
            extracted_texts.extend(left_texts)            

            return above_texts,left_texts,extracted_texts
        else:
            print("\n未识别到任何文字区域，可能原因:")
            print("1. 图像不包含文字")
            print("2. 文字区域太小")
            print("3. OCR引擎配置问题")
            return results,results,results

        # 可视化结果
        output_path = "ocr_result_visualization.png"
        visualize_ocr_results(test_image_path, results, output_path)
        print(f"可视化结果已保存至: {output_path}")
    except Exception as e:
        print(f"\nOCR测试失败: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理测试文件
        if os.path.exists("ocr_result_visualization.png"):
            os.remove("ocr_result_visualization.png")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    above_texts,left_texts,extracted_texts=test_ocr_function("input/4.png")

    print(f"找到目标上方文本: {len(above_texts)}个")
    for i, text in enumerate(above_texts, 1):
        print(f"  上方文本{i}: '{text.text}' (位置: {text.get_center()})")
    
    print(f"找到目标左边文本: {len(left_texts)}个")
    for i, text in enumerate(left_texts, 1):
        print(f"  左边文本{i}: '{text.text}' (位置: {text.get_center()})")

    print(f"找到目标周围文本: {len(extracted_texts)}个")
    for i, text in enumerate(extracted_texts, 1):
        print(f"  周围文本{i}: '{text.text}' (位置: {text.get_center()})")

    


