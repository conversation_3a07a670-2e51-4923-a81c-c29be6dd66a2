from docx import Document
from docx.oxml.ns import qn
from io import BytesIO
from typing import Union, List, Tuple

def extract_named_image(docx_bytes: bytes, figure_name: str) -> Union[bytes, None]:
    """ 从DOCX文件中提取与指定图名对应的图片，假设图名在图片后的段落中 """

    # 加载DOCX文件
    try:
        doc = Document(BytesIO(docx_bytes))
    except Exception as e:
        return None

    # 存储图片和图名位置
    images: List[Tuple[int, str, str, str]] = []  # (block_index, rId, image_type, location)
    figure_positions: List[Tuple[int, str]] = []  # (block_index, location)
    block_index = 0
    ns = {
        'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
        'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
        'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'
    }

    # 检查图片的函数
    def check_images(element, block_index: int, location: str):
        for run_idx, run in enumerate(element.findall('.//w:r', ns)):
            for drawing in run.findall('.//w:drawing', ns):
                for blip in drawing.findall('.//a:blip', ns):
                    rId = blip.get(qn('r:embed'))
                    if rId and rId in doc.part.rels:
                        image_type = doc.part.rels[rId].target_ref.split('.')[-1]
                        images.append((block_index, rId, image_type, location))

    # 遍历正文段落
    for para_idx, para in enumerate(doc.paragraphs):
        text = para.text.strip()
        if figure_name in text:
            figure_positions.append((block_index, f"正文段落 {para_idx}"))
        check_images(para._element, block_index, f"正文段落 {para_idx}")
        block_index += 1

    # 遍历表格
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                for para_idx, para in enumerate(cell.paragraphs):
                    text = para.text.strip()
                    if figure_name in text:
                        figure_positions.append((block_index, f"表格 {table_idx}, 行 {row_idx}, 单元格 {cell_idx}, 段落 {para_idx}"))
                    check_images(para._element, block_index, f"表格 {table_idx}, 行 {row_idx}, 单元格 {cell_idx}, 段落 {para_idx}")
                    block_index += 1

    # 遍历文本框
    for txbx_idx, txbx in enumerate(doc.element.body.findall('.//w:txbxContent', ns)):
        for para_idx, para in enumerate(txbx.findall('.//w:p', ns)):
            text = ''.join(t.text for t in para.findall('.//w:t', ns) if t.text).strip()
            if figure_name in text:
                figure_positions.append((block_index, f"文本框 {txbx_idx}, 段落 {para_idx}"))
            check_images(para, block_index, f"文本框 {txbx_idx}, 段落 {para_idx}")
            block_index += 1

    # 寻找最近前置图片
    for fig_pos, fig_location in sorted(figure_positions):
        closest_image = None
        min_distance = float('inf')
        for img_pos, rId, img_type, img_location in images:
            if img_pos < fig_pos:  # 确保图片在图名前
                distance = fig_pos - img_pos
                if distance < min_distance:
                    min_distance = distance
                    closest_image = (rId, img_type, img_location)
        if closest_image:
            rId, img_type, img_location = closest_image
            return doc.part.related_parts[rId].blob

    return None

# 测试代码
if __name__ == "__main__":
    with open("中集阀门-内一审修改.docx", "rb") as f:
        doc_bytes = f.read()

    figure_name = "工业废水处理系统流程图"
    image_bytes = extract_named_image(docx_bytes=doc_bytes, figure_name=figure_name)
    if image_bytes:
        with open("extracted_image.png", "wb") as img_file:
            img_file.write(image_bytes)
        print("图片已提取并保存为 'extracted_image.png'")
    else:
        print("未找到与图名对应的图片，请检查日志")