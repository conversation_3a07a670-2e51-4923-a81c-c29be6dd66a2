from docx import Document
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import Table
from docx.text.paragraph import Paragraph
from io import BytesIO
from typing import Union

def extract_named_table(docx_bytes: bytes, keyword: str) -> Union[list[list[str]], None]:
    """ 从 Word 字节流中查找包含 keyword 的段落，返回其后第一个表格（支持嵌套结构）。 """
    doc = Document(BytesIO(docx_bytes))
    blocks = []

    def walk(element, parent):
        for child in element.iterchildren():
            if isinstance(child, CT_P):
                blocks.append(('p', Paragraph(child, parent)))
            elif isinstance(child, CT_Tbl):
                table = Table(child, parent)
                blocks.append(('tbl', table))
                for row in table.rows:
                    for cell in row.cells:
                        walk(cell._element, cell)

    def extract_table(table: Table) -> list[list[Union[str, list]]]:
        result = []
        for row in table.rows:
            row_data = []
            for cell in row.cells:
                if cell.tables:
                    nested = [extract_table(t) for t in cell.tables]
                    row_data.append(nested[0] if len(nested) == 1 else nested)
                else:
                    row_data.append(cell.text.strip())
            result.append(row_data)
        return result

    walk(doc.element.body, doc)

    for i, (typ, obj) in enumerate(blocks):
        if typ == 'p' and keyword in obj.text:
            for typ2, tbl in blocks[i + 1:]:
                if typ2 == 'tbl':
                    return extract_table(tbl)
            break

    return None

if __name__ == "__main__":
    with open(r"C:\Users\<USER>\Downloads\1、大石环评文本.docx", "rb") as f:
        doc_bytes = f.read()

    result = extract_named_table(doc_bytes, "建设项目基本情况")
    
    if result:
        from pprint import pprint
        pprint(result)
    else:
        print("未找到表格")