"""
集成示例：如何在现有代码中使用EMF到PNG转换器

该示例展示了如何将emf_to_png_converter集成到现有的EMF处理流程中
"""

import os
import sys
from io import BytesIO

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from SearchContent.emf_to_png_converter import (
    convert_emf_to_high_quality_png,
    convert_emf_file_to_png,
    batch_convert_emf_to_png
)


def enhanced_extract_emf_images(doc_bytes, output_dir, use_high_quality=True, dpi=800):
    """
    增强版EMF图像提取函数，使用高质量转换器
    
    这是对原始extract_emf_images函数的改进版本
    """
    from docx import Document
    import re
    import json
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        doc_stream = BytesIO(doc_bytes)
        doc = Document(doc_stream)
    except Exception as e:
        raise ValueError("文件格式错误或非.docx文件") from e
    
    relations = []
    pattern = re.compile(r'rId(\d+)')
    
    # 遍历表格
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            for cell_idx, cell in enumerate(row.cells):
                
                # 提取文本框内容
                ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
                text_parts = []
                textboxes = cell._element.findall('.//w:txbxContent', ns)
                for textbox in textboxes:
                    for paragraph in textbox.findall('.//w:p', ns):
                        para_text = ''.join(run.text for run in paragraph.findall('.//w:t', ns))
                        text_parts.append(para_text)

                count = 0
                cell_text = cell.text.strip()
                
                # 检查单元格内图片
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        match = pattern.search(run._element.xml)
                        if match:
                            rId = f'rId{match.group(1)}'
                            
                            rel = doc.part.rels[rId]
                            if "image" in rel.target_ref:
                                if rel.target_ref.endswith('.emf'):
                                    # EMF图片处理
                                    emf_data = rel.target_part.blob
                                    
                                    # 保存原始EMF文件
                                    emf_path = os.path.join(
                                        output_dir, 
                                        f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.emf'
                                    )
                                    count += 1
                                    with open(emf_path, 'wb') as f:
                                        f.write(emf_data)
                                    
                                    # 使用高质量转换器转换为PNG
                                    png_path = emf_path.replace('.emf', '.png')
                                    if use_high_quality:
                                        try:
                                            convert_emf_to_high_quality_png(
                                                emf_data, 
                                                png_path, 
                                                dpi=dpi, 
                                                enhance_quality=True
                                            )
                                            print(f"高质量转换完成: {png_path}")
                                        except Exception as e:
                                            print(f"高质量转换失败，使用默认方法: {str(e)}")
                                            # 回退到原始方法
                                            _fallback_convert_emf_to_png(emf_data, png_path)
                                    else:
                                        _fallback_convert_emf_to_png(emf_data, png_path)
                                    
                                    # 建立关系映射
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": emf_path,
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                        "conversion_method": "high_quality" if use_high_quality else "standard"
                                    })
                                    
                                elif rel.target_ref.endswith('.png'):
                                    # PNG图片直接处理
                                    png_data = rel.target_part.blob
                                    png_path = os.path.join(
                                        output_dir, 
                                        f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.png'
                                    )
                                    count += 1
                                    with open(png_path, 'wb') as f:
                                        f.write(png_data)
                                    
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": None,
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                        "conversion_method": "direct_png"
                                    })
    
    # 保存关系数据
    with open(os.path.join(output_dir, 'relations.json'), 'w', encoding='utf-8') as f:
        json.dump(relations, f, indent=2, ensure_ascii=False)
    
    return relations


def _fallback_convert_emf_to_png(emf_data, png_path):
    """回退的EMF转PNG方法"""
    from PIL import Image
    
    try:
        emf_image = Image.open(BytesIO(emf_data))
        if emf_image.mode in ('RGBA', 'LA'):
            bg = Image.new("RGB", emf_image.size, (255, 255, 255))
            bg.paste(emf_image, mask=emf_image.split()[3])
            processed_image = bg
        else:
            processed_image = emf_image
        
        processed_image.save(png_path, format="PNG", dpi=(300, 300))
    except Exception as e:
        print(f"回退转换也失败: {str(e)}")


def batch_process_existing_emf_files(emf_directory, output_directory, dpi=800):
    """
    批量处理现有的EMF文件
    
    参数:
        emf_directory: 包含EMF文件的目录
        output_directory: PNG输出目录
        dpi: 输出分辨率
    """
    if not os.path.exists(emf_directory):
        print(f"EMF目录不存在: {emf_directory}")
        return
    
    # 收集所有EMF文件
    emf_files = {}
    for filename in os.listdir(emf_directory):
        if filename.lower().endswith('.emf'):
            emf_path = os.path.join(emf_directory, filename)
            with open(emf_path, 'rb') as f:
                emf_files[filename] = f.read()
    
    if not emf_files:
        print(f"在 {emf_directory} 中未找到EMF文件")
        return
    
    print(f"找到 {len(emf_files)} 个EMF文件，开始批量转换...")
    
    # 批量转换
    results = batch_convert_emf_to_png(
        emf_files, 
        output_directory, 
        dpi=dpi, 
        enhance_quality=True
    )
    
    # 统计结果
    success_count = sum(1 for result in results.values() if result is not None)
    print(f"转换完成: {success_count}/{len(emf_files)} 个文件成功")
    
    return results


def compare_conversion_quality():
    """
    比较不同转换方法的质量
    """
    # 测试文件路径
    test_emf = "../../output_emf_images/table1_row0_cell1_0.emf"
    
    if not os.path.exists(test_emf):
        print("测试EMF文件不存在，跳过质量比较")
        return
    
    print("=== 转换质量比较 ===")
    
    # 读取EMF数据
    with open(test_emf, 'rb') as f:
        emf_data = f.read()
    
    # 不同DPI设置的转换
    dpi_settings = [300, 600, 800, 1200]
    
    for dpi in dpi_settings:
        output_path = f"quality_test_{dpi}dpi.png"
        try:
            convert_emf_to_high_quality_png(
                emf_data, 
                output_path, 
                dpi=dpi, 
                enhance_quality=True
            )
            
            # 检查文件大小
            file_size = os.path.getsize(output_path)
            print(f"DPI {dpi}: {output_path} - {file_size:,} bytes")
            
        except Exception as e:
            print(f"DPI {dpi}: 转换失败 - {str(e)}")


if __name__ == "__main__":
    print("=== EMF转换器集成示例 ===")
    
    # 示例1: 批量处理现有EMF文件
    print("\n1. 批量处理现有EMF文件:")
    batch_process_existing_emf_files(
        "../../output_emf_images", 
        "enhanced_png_output",
        dpi=800
    )
    
    # 示例2: 质量比较
    print("\n2. 转换质量比较:")
    compare_conversion_quality()
    
    print("\n集成示例完成！")
    print("生成的高质量PNG文件可用于:")
    print("- OCR文字识别")
    print("- 图像分析")
    print("- 文档处理")
    print("- 打印输出")
