import re
from typing import Dict, Any
from shared.llm_chat import LLMChat

class DictComparator:
    """
    功能：比较两个字典中共有键对应的值，并借助大模型判断它们是否表示相同含义。若不相同，则将该键和对应值存入返回结果。
    使用示例：
        comparator = DictComparator(llm)
        diff = comparator.compare(dict_a, dict_b)
    返回：
        diff: Dict[str, Dict[str, Any]]，结构类似 {'字段1': {'value1': ..., 'value2': ...}, ...}
    """
    def __init__(self, llm: LLMChat):
        self.llm = llm

    def compare(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        比较两个字典中共有的键，询问大模型判断其对应值是否相同。

        :param dict1: 第一个字典
        :param dict2: 第二个字典
        :return: 包含所有大模型判定为不相同键的结果字典
        """
        common_keys = set(dict1.keys()) & set(dict2.keys())
        differences: Dict[str, Dict[str, Any]] = {}

        for key in common_keys:
            val1 = dict1.get(key)
            val2 = dict2.get(key)

            if val1 == val2:
                continue

            # 将值转换为字符串以确保提示词中的正确显示
            val1_str = str(val1)
            val2_str = str(val2)

            prompt = (
                "请严格判断以下两个值在语义上是否冲突，忽略格式、单位、符号或表达方式的差异。\n"
                "注意：只回答‘是’或‘否’，不要任何解释。\n"
                f"值1：{val1_str}\n"
                f"值2：{val2_str}"
            )
            response = self.llm.chat(prompt)
            answer = response.strip().lower()
            
            # 清理回答中的非文字/数字字符，并检查否定词
            answer_clean = re.sub(r'[^a-z0-9\u4e00-\u9fff]', '', answer)
            if answer_clean.startswith(('否', '不', 'n')):
                differences[key] = {"value1": val1, "value2": val2}

        return differences


if __name__ == "__main__":
    # 测试用例
    from config import MODEL_CONFIG
    llm = LLMChat(
        url=MODEL_CONFIG['url'],
        key=MODEL_CONFIG['key'],
        model=MODEL_CONFIG['model']
    )

    comparator = DictComparator(llm)

    dict_a = {'建设项目名称': '年产8000万件电子零部件制造', '项目代码': '2405-320692-89-01-935692', '建设地点': '江苏省南通市江苏省通州湾江海联动开发示范区电子信息产业园经六路618号', '面积（m2)': '2700', '备案文号（选填)': '通州湾行审备（2024）626号', '总投资（万元)': '200', '建设性质': '新建', '备案）文号（选填)': '通州湾管发［2020］15号'}
    dict_b = {'建设项目名称': '年产8000万套电子零部件制造', '项目代码': '2405-320692-89-01-935692', '建设地点': '江苏省通州湾江海联动开发示范区电子信息产业园经六路618号', '建设性质': 'R新建（迁建）'}

    diffs = comparator.compare(dict_a, dict_b)
    print("Differences:", diffs)  # 示例输出可能为空或包含特定键，根据模型判断