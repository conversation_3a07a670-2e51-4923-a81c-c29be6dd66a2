import streamlit as st
from shared.audit_page import AuditPage  # 替换为你的实际路径
from plugins.site import SiteProcessor

def init_site_processor():
    """初始化地点一致性处理器
    
    功能：
    - 设置地点一致性审查界面
    - 准备地点审查所需参数
    
    参数：无
    返回值：无
    """
    st.write(f"使用默认参数进行自动审查")
    return SiteProcessor()

# 页面入口
if __name__ == "__main__":
    page = AuditPage(init_site_processor,'地点一致性审核')
    page.render()
