from typing import List

def merge_header(table: List[List[str]], sep: str = "@") -> List[List[str]]:
    if not isinstance(table, list) or not table:
        return table
    if not isinstance(table[0], (list, tuple)) or len(table[0]) < 2:
        return table
    if len(table) < 2:
        return table

    r1, r2 = table[0], table[1]

    # 判断第一行是否存在任意两个相邻元素相等
    if not any(r1[i] == r1[i+1] for i in range(len(r1) - 1)):
        return table  # 没有相邻相同 → 不合并

    n = max(len(r1), len(r2))
    merged = [
        r1[i] if i >= len(r2) or (i < len(r1) and r1[i] == r2[i])
        else (r1[i] if i < len(r1) else "") + sep + (r2[i] if i < len(r2) else "")
        for i in range(n)
    ]
    return [merged] + table[2:]

if __name__ == '__main__':
    print(merge_header([["A", "A", "B"], ["A", "BB", "CC"], ["1", "2", "3"]]))  # ✅ 合并
    print(merge_header([["A", "B", "C"], ["A", "BB", "CC"], ["1", "2", "3"]]))  # ❌ 不合并
    print(merge_header([["X", "X"], ["Y", "Z"], ["1", "2"]]))                  # ✅ 合并
    print(merge_header([["A"], ["B"], ["1"]]))                                 # ❌ 不合并
