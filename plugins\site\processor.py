import re
from typing import Union, Iterable, List, Tuple
from shared.custom_document import CustomDocument
from .config import SITES

class SiteProcessor():
    def __init__(self, allowed_sites: Union[str, Iterable[str]] = SITES):
        if isinstance(allowed_sites, str):
            self.sites = [allowed_sites]
        else:
            self.sites = list(allowed_sites)

        # 构建一个正则，用于全文扫描所有地点（加分组以便提取）
        self._pattern = re.compile("|".join(f"({re.escape(site)})" for site in self.sites))

    def __call__(self, doc_bytes: bytes) -> bytes:
        doc = CustomDocument(doc_bytes)
        marked_text = doc.get_marked_text()

        # 找出所有匹配的位置
        matches: List[Tuple[int, str]] = [(m.start(), m.group()) for m in self._pattern.finditer(marked_text)]

        if not matches:
            return doc.get_docx_bytes()  # 未发现任何地点，跳过处理

        # 第一次出现的合法地点作为标准地点
        main_site = matches[0][1]

        # 其他地点若不同，插入批注
        comments = []
        for pos, site in matches[1:]:
            if site != main_site:
                comments.append((pos, f"文中地点不一致：应为“{main_site}”，但出现了“{site}”"))

        # 插入批注
        if comments:
            doc.insert_comment_at_position(comments, author="区位一致性审查")

        return doc.get_docx_bytes()
