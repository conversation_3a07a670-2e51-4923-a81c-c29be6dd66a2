from docx import Document
from io import BytesIO

def comment_first(doc_bytes: bytes, target: str, comment: str, author: str = "审阅者") -> bytes:
    """在DOCX文件中首次出现目标文本处添加注释
    
    参数:
        doc_bytes: DOCX文件的字节内容
        target: 需要添加注释的目标文本
        comment: 要添加的注释内容
        author: 注释作者，默认为"审阅者"
        
    返回值:
        bytes: 添加注释后的DOCX文件字节内容
    """
    doc, found = Document(BytesIO(doc_bytes)), False

    def mark(paras):
        nonlocal found
        for p in paras:
            for run in p.runs:
                if found or target not in run.text: continue
                run.text, mid, post = run.text.partition(target)
                r = p.add_run(mid); p.add_run(post)
                r.bold, r.italic, r.underline = run.bold, run.italic, run.underline
                doc.add_comment(runs=r, text=comment, author=author)
                found = True; return

    def scan(tbl): 
        for row in tbl.rows:
            for cell in row.cells:
                mark(cell.paragraphs)
                for t in cell.tables: scan(t)

    mark(doc.paragraphs)
    if not found:
        for t in doc.tables: scan(t)

    out = BytesIO(); doc.save(out)
    return out.getvalue()

if __name__ == "__main__":
    with open("input.docx", "rb") as f:
        original = f.read()

    result = comment_first(original, "排气筒底部", "请确认此处术语")

    with open("annotated.docx", "wb") as f:
        f.write(result)
