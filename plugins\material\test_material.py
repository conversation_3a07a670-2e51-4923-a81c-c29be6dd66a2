import sys
import os
from .processor import MaterialProcessor

if __name__ == "__main__":
    #运行命令
    #D:\anaconda3\envs\doctest\python.exe -m plugins.material.test_material -d sample2.docx
    # 使用示例
    #doc_path = "input/sample6.docx";tablename="建设项目主要原辅料情况表";columnnames=["序号","名称","成分/形态"];graphname="液压成套设备工艺流程及产污环节示意图";
    #doc_path = "input/主要原辅材料情况表：安全阀生产工艺流程图.docx";tablename="主要原辅材料情况表";columnnames=["序号","名称","组分"];graphname="安全阀生产工艺流程图";
    #doc_path = "input/主要原辅料情况表：干法人造石板材生产工艺流程及产污节点图.docx";tablename="主要原辅料情况表";columnnames=["原料名称","形态/成分"];graphname="干法人造石板材生产工艺流程及产污节点图";
    #doc_path = "input/主要原辅材料消耗情况：模具生产工艺流程图.docx";tablename="主要原辅材料消耗情况";columnnames=["序号","名称","组分"];graphname="模具生产工艺流程图";
    
    # 默认路径
    base_path = r"C:\Users\<USER>\Desktop\test-doc"
    doc_filename = "sample2.docx"
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        for i, arg in enumerate(sys.argv):
            if arg == "-d" and i + 1 < len(sys.argv):
                doc_filename = sys.argv[i + 1]
                break
    
    # 拼接完整路径
    doc_path = os.path.join(base_path, doc_filename)
    
    print(f"处理文件: {doc_path}")
    
    with open(doc_path, "rb") as file:
        word_bytes = file.read()
    
    processor = MaterialProcessor()
    new_bytes = processor(word_bytes)

    with open("output\output.docx", "wb") as f:
        f.write(new_bytes)
