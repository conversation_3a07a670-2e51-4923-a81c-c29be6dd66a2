import json
import re
from typing import List, Dict, Any
import fitz  # PyMuPDF
import pytesseract
from shared.llm_chat import LLMChat  # 假设已提供

class PDFExtractorSimple:
    """
    功能：从 PDF 文件中提取文本，并使用大模型（LLM）提取指定的字段。
    """
    def __init__(self, llm: LLMChat, lang: str = "chi_sim", max_pages: int = 3):
        """初始化 PDFExtractorSimple 实例。"""
        self.llm = llm
        self.lang = lang
        self.max_pages = max_pages

    def is_text_based(self, pdf_bytes: bytes) -> bool:
        """判断 PDF 是否为文本型 PDF。"""
        try:
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            total_text = ""
            for page in doc:
                total_text += page.get_text() # type: ignore
            doc.close()
            return len(total_text.strip()) > 10
        except Exception as e:
            print(f"[错误] 检查 PDF 类型失败: {e}")
            return False

    def extract_text(self, pdf_bytes: bytes) -> str:
        """
        从 PDF 文件中提取文本。
        
        :param pdf_bytes: PDF 文件的字节数据。
        :return: 提取的文本内容。
        """
        try:
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            if self.is_text_based(pdf_bytes):
                # 文本型 PDF，直接提取文本
                text = ""
                for page in doc[:self.max_pages]:
                    text += page.get_text() # type: ignore
                doc.close()
                return text
            else:
                # 扫描型 PDF，使用 OCR 提取文本
                text_list = []
                for i, page in enumerate(doc[:self.max_pages]):
                    pix = page.get_pixmap()  # type: ignore # 渲染成图片
                    img_text = pytesseract.image_to_string(pix.tobytes("png"), lang=self.lang)
                    text_list.append(f"------ 第 {i+1} 页 ------\n{img_text}")
                doc.close()
                return "\n".join(text_list)
        except Exception as e:
            print(f"[错误] 提取文本失败: {e}")
            return ""

    def extract(self, pdf_list: List[bytes], fields: List[str]) -> Dict[str, Any]:
        """
        从多个 PDF 文件中提取指定字段，并合并结果。
        
        :param pdf_list: PDF 文件字节数据的列表。
        :param fields: 需要提取的字段列表。
        :return: 提取的字段和值的字典。
        """
        combined_result = {}
        for i, pdf_bytes in enumerate(pdf_list):
            try:
                text = self.extract_text(pdf_bytes)
                if not text:
                    print(f"[警告] 第 {i+1} 个 PDF 未提取到文本")
                    continue
                prompt = self._build_prompt(text, fields)
                response = self.llm.chat(prompt)
                data = self._parse_json(response)
                if isinstance(data, dict):
                    for key, value in data.items():
                        if key not in combined_result and value not in [None, "null", ""]:
                            combined_result[key] = value
            except Exception as e:
                print(f"[错误] 处理第 {i+1} 个 PDF 失败: {e}")
        return combined_result

    def _build_prompt(self, text: str, fields: List[str]) -> str:
        """构建用于大模型的提示词。"""
        fields_json = json.dumps(fields, ensure_ascii=False)
        return (
            f"你是结构化信息提取引擎。请从以下PDF文本中提取指定字段，"
            f"并严格返回符合 JSON 语法的字符串（不要加 Markdown 代码块标记 ```，也不要多余的解释、注释或额外文本）。\n"
            f"目标字段: {fields_json}\n"
            f"输入文本开始——\n{text[:2000]}\n输入文本结束——"
        )

    def _parse_json(self, response: str) -> Any:
        """解析大模型返回的 JSON 响应。"""
        response = response.strip()
        match = re.search(r"```(?:json)?\s*(\{.*?\})\s*```", response, re.DOTALL)
        if match:
            cleaned = match.group(1).strip()
        else:
            match = re.search(r"(\{.*?\})", response, re.DOTALL)
            if match:
                cleaned = match.group(1).strip()
            else:
                raise ValueError(f"未能提取出 JSON 内容, 原始响应: {response}")
        try:
            return json.loads(cleaned)
        except json.JSONDecodeError as e:
            raise ValueError(f"解析JSON失败: {e}, 内容: {cleaned}")

if __name__ == "__main__":
    from config import MODEL_CONFIG
    llm = LLMChat(MODEL_CONFIG['url'], MODEL_CONFIG['key'], MODEL_CONFIG['model'])
    extractor = PDFExtractorSimple(llm)

    # 这里用你自己的 PDF 文件路径列表加载成 bytes
    pdf_paths = [
        "example1.pdf",
        "example2.pdf",
        # ...
    ]

    pdf_bytes_list = []
    for path in pdf_paths:
        try:
            with open(path, "rb") as f:
                pdf_bytes_list.append(f.read())
        except Exception as e:
            print(f"[错误] 读取文件 {path} 失败: {e}")

    fields = [
        "建设项目名称", "项目名称", "项目代码", "建设地点",
        "面积（m2）", "备案）文号（选填）", "总投资（万元）", "建设性质"
    ]

    result = extractor.extract(pdf_bytes_list, fields)
    print(json.dumps(result, ensure_ascii=False, indent=2))