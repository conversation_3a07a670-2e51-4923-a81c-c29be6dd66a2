from typing import Optional

from spire.doc import Document, Comment, CommentMark, CommentMarkType, FileFormat
from spire.doc.common import Stream # type: ignore


class DocxAnnotator:
    def __init__(self, doc_bytes: bytes) -> None:
        """
        使用字节数据初始化 Document 对象。
        """
        # 从 Stream 加载文档
        self.doc = Document(Stream(doc_bytes)) # type: ignore
        # 提取纯文本并拆分成单词列表，方便查找
        self.words = self.get_plain_text().split()

    def get_plain_text(self) -> str:
        """
        获取文档中的所有文本内容，返回一个字符串。
        """
        return self.doc.GetText()

    def find_next_field(self, keyword: str) -> Optional[str]:
        """
        在文档纯文本单词列表中查找关键字，并返回关键字后面的下一个单词，若不存在或未找到则返回 None。
        """
        try:
            idx = self.words.index(keyword)
            return self.words[idx + 1] if idx + 1 < len(self.words) else None
        except ValueError:
            return None

    def annotate_field(self, keyword: str, comment_text: str, author: Optional[str] = None) -> bool:
        """
        在文档中查找指定关键字，并在该关键字所在位置插入批注（Comment）。
        返回 True 表示批注已成功添加，False 表示未找到关键字。
        """
        text_obj = self.doc.FindString(keyword, True, True) # type: ignore
        if not text_obj:
            return False

        text_range = text_obj.GetAsOneRange()
        paragraph = text_range.OwnerParagraph

        # 创建 Comment 对象，并设置批注内容
        comment = Comment(self.doc)
        comment.Body.AddParagraph().Text = comment_text
        if author:
            comment.Format.Author = author

        # 创建批注开始和结束标记
        comment_start = CommentMark(self.doc, CommentMarkType.CommentStart)
        comment_end = CommentMark(self.doc, CommentMarkType.CommentEnd)
        comment_start.CommentId = comment.Format.CommentId
        comment_end.CommentId = comment.Format.CommentId

        # 将批注标记和 Comment 对象插入到段落中的适当位置
        idx = paragraph.ChildObjects.IndexOf(text_range)
        paragraph.ChildObjects.Insert(idx, comment_start)
        paragraph.ChildObjects.Insert(idx + 1, comment_end)
        paragraph.ChildObjects.Insert(idx + 2, comment)

        return True

    def save(self) -> bytes:
        """
        将 Document 对象保存到内存字节流，并返回生成的字节数组。
        """
        output_stream = Stream()
        self.doc.SaveToStream(output_stream, FileFormat.Docx)
        # 关闭文档（可选，但推荐）
        self.doc.Close()
        return output_stream.ToArray()


# 示例用法
if __name__ == "__main__":
    # 从本地文件读取字节数据（仅供演示，生产代码中可能直接从其他来源获取 bytes）
    with open(r"input.docx", "rb") as f:
        word_bytes = f.read()

    annotator = DocxAnnotator(word_bytes)

    fields = [
        "建设项目名称",
        "项目代码",
        "建设地点",
        "面积（m2）",
        "备案）文号（选填）",
        "总投资（万元）",
        "建设性质",
    ]
    results: dict[str, Optional[str]] = {}

    for field in fields:
        results[field] = annotator.find_next_field(field)

    for key, value in results.items():
        print(f"{key}: {value}")

    # 添加针对“建设项目名称”的批注
    if annotator.annotate_field("建设项目名称:", "请确认项目名称是否准确。"):
        print("批注已添加：建设项目名称")

    # 将修改后的文档保存为字节，并写入文件
    new_bytes = annotator.save()
    with open(r"TextComment.docx", "wb") as f:
        f.write(new_bytes)
