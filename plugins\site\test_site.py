from . import SiteProcessor

if __name__ == "__main__":
    from docx import Document
    from io import BytesIO

    # 构造测试文档
    d = Document()
    d.add_paragraph("我们在北京召开会议，然后转往上海和广州进行考察。")
    buf = BytesIO()
    d.save(buf)
    buf.seek(0)

    # 初始化 SiteProcessor，允许的地点包括多个
    processor = SiteProcessor(["北京", "上海", "广州"])
    result = processor.process(buf.getvalue())

    # 输出到本地文件查看结果
    with open("site_check.docx", "wb") as f:
        f.write(result)

    print("已完成地点一致性检查。")
