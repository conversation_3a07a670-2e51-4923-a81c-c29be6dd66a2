from typing import List, Dict, Optional
from bisect import bisect_right
from collections import defaultdict


class Node:
    """
    表示章节树中的一个节点。
    """
    def __init__(self, text: str, level: int):
        """
        初始化节点实例。

        参数:
            text (str): 节点文本内容。
            level (int): 节点在树中的层级。
        """
        self.text: str = text
        self.level: int = level
        self.children: List["Node"] = []
        self.parent: Optional["Node"] = None
        self.index_in_text: Optional[int] = None
        self.matched: bool = False

    def add_child(self, child: "Node"):
        """
        添加子节点。

        参数:
            child (Node): 要添加的子节点。
        """
        self.children.append(child)
        child.parent = self

    def print_tree(self, indent=0):
        """
        打印节点及其子节点的树结构。

        参数:
            indent (int): 当前节点的缩进级别。
        """
        prefix = "  " * indent
        print(f"{prefix}{self.text} (matched={self.matched}, pos={self.index_in_text})")
        for c in self.children:
            c.print_tree(indent+1)


class ChapterTree:
    """
    表示文档的章节树结构，用于分析和匹配文档中的标题层次。
    """
    def __init__(self, yaml_str: str):
        """
        初始化 ChapterTree 实例。

        参数:
            yaml_str (str): 包含章节结构的 YAML 字符串。
        """
        self.yaml_str = yaml_str
        self.nodes: List[Node] = []
        self._parse_yaml_lines()

        # 初始化 root
        self.root = Node("ROOT", -1)
        self.root.index_in_text = 0
        self.root.matched = True

        self.failed_nodes: Dict[str, int] = {}
        self.occurrences: Dict[str, List[int]] = {}

    def _parse_yaml_lines(self):
        """
        按行手动解析 YAML 结构，生成 self.nodes 列表。
        """
        for line in self.yaml_str.splitlines():
            if not line.strip():
                continue
            level = (len(line) - len(line.lstrip(' '))) // 2
            text = line.strip().lstrip('-').strip()
            self.nodes.append(Node(text, level))

    def set_text(self, text: str):
        """设置文档文本并构建章节树
        
        功能：
        - 分析文档文本中的标题出现位置
        - 构建章节树结构
        - 记录匹配失败的节点
        
        参数:
            text (str): 要分析的文档文本
        """
        # 1. 构建所有关键词的出现位置
        self.occurrences = defaultdict(list)
        for node in self.nodes:
            start = text.find(node.text)
            while start != -1:
                self.occurrences[node.text].append(start)
                start = text.find(node.text, start + 1)
        for lst in self.occurrences.values():
            lst.sort()

        # 2. 构建树 & 记录失配
        self._match_and_build_tree()

    def _match_and_build_tree(self):
        """
        内部方法：匹配文本并构建章节树。
        """
        # 重置
        self.root.children.clear()
        self.failed_nodes.clear()

        stack = [self.root]
        i = 0
        n = len(self.nodes)

        while i < n:
            node = self.nodes[i]

            # 找到父节点（比自己层级小的最近节点）
            while stack and stack[-1].level >= node.level:
                stack.pop()
            parent = stack[-1]

            # 计算 valid_start
            # 父节点结束位置：
            if parent is self.root:
                parent_end = 0
            else:
                parent_end = (parent.index_in_text or 0) + len(parent.text)
            # 前一个兄弟结束位置：
            prev = parent.children[-1] if parent.children else None
            if prev:
                sib_end = (prev.index_in_text or 0) + len(prev.text)
            else:
                sib_end = 0

            valid_start = max(parent_end, sib_end)

            # 在 occurrences 中二分查找第一个 >= valid_start
            positions = self.occurrences.get(node.text, [])
            idx = bisect_right(positions, valid_start - 1)
            if idx < len(positions):
                # 匹配成功
                pos = positions[idx]
                node.index_in_text = pos
                node.matched = True
                parent.add_child(node)
                stack.append(node)
                i += 1
            else:
                # 失配：记录挂载父节点的 index_in_text
                self.failed_nodes[node.text] = parent.index_in_text or 0
                # 跳过其所有子节点
                base_level = node.level
                i += 1
                while i < n and self.nodes[i].level > base_level:
                    i += 1

        # 匹配完成

    def print_tree(self):
        """
        打印整个章节树结构。
        """
        self.root.print_tree()

    def get_failed_nodes(self) -> Dict[str, int]:
        """
        获取所有匹配失败的节点及其位置。

        返回:
            Dict[str, int]: 包含失败节点及其位置的字典。
        """
        return self.failed_nodes


if __name__ == "__main__":
    yaml_str = """
- 第一章
  - 第一节
    - 小节A
  - 第二节
- 第二章
"""
    text = "第一章第一节第二章"

    tree = ChapterTree(yaml_str)
    tree.set_text(text)
    tree.print_tree()
    print("失配节点及对应父节点索引:", tree.get_failed_nodes())
