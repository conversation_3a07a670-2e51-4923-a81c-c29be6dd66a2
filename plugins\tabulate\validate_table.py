from typing import List, Union
import re
from asteval import Interpreter


def match_token_to_header(token: str, header: List[str]) -> Union[int, None]:
    """在 header 中找最短匹配 token 的列索引，找不到返回 None。"""
    candidates = [(j, h) for j, h in enumerate(header) if token in h]
    if not candidates:
        return None
    return min(candidates, key=lambda x: len(x[1]))[0]


def validate_table(table: List[List[str]], rules: Union[str, List[str]], tol: float = 1e-4) -> List[str]:
    """
    验证 table（header+rows）是否满足 rules（或单条 rule），支持：
      - 中文/下划线/@ 列名
      - 浮点容差 tol
      - 只转换表达式中真正用到的列
    返回错误信息列表，空列表表示全通过。
    """
    if len(table) < 2:
        return ["表格行数不足"]
    header, *rows = table
    rules = [rules] if isinstance(rules, str) else rules
    if not rules:
        return ["未提供规则"]

    # 匹配 “左 操作符 右”
    expr_pattern  = re.compile(r"\s*(.+?)\s*(==|!=|>=|<=|>|<|=)\s*(.+)", re.UNICODE)
    # 匹配列名 token（首字符非数字，后续可有字母、数字、下划线、中文或 @）
    token_pattern = re.compile(r"[^\d\W][\w@\u4e00-\u9fff]*", re.UNICODE)

    # 列索引 → 变量名映射
    idx_var = {i: f"var{i}" for i in range(len(header))}
    rule_maps = []

    # 1. 解析规则，替换为可执行表达式
    for idx, expr in enumerate(rules, start=1):
        m = expr_pattern.match(expr)
        if not m:
            return [f"规则{idx} 无效：{expr!r}"]
        L, op, R = m.group(1), m.group(2), m.group(3)

        # 抽取所有可能的列名 token（剔除 and/or/not/纯数字）
        tokens = {
            t for t in token_pattern.findall(expr)
            if t.lower() not in {'and', 'or', 'not'} and not t.isdigit()
        }
        # token → header 列索引
        tok_idx = {t: match_token_to_header(t, header) for t in tokens}
        if None in tok_idx.values():
            bad = next(t for t, j in tok_idx.items() if j is None)
            return [f"规则{idx} 含未知列名：{bad}"]

        L_expr, R_expr = L, R
        # 按 token 长度从长到短，替换为变量名
        for t, j in sorted(tok_idx.items(), key=lambda x: -len(x[0])):
            var = idx_var[j]
            # 用 (?<!\w)...(?!\w) 支持中文边界
            pattern = rf"(?<!\w){re.escape(t)}(?!\w)"
            L_expr = re.sub(pattern, var, L_expr)
            R_expr = re.sub(pattern, var, R_expr)

        # 确定哪些 var 真正出现在 L_expr 或 R_expr 中
        used_vars = {
            idx_var[j]
            for j in tok_idx.values()
            if idx_var[j] in L_expr or idx_var[j] in R_expr
        }

        rule_maps.append({
            'orig': expr,
            'op': op,
            'L_expr': L_expr,
            'R_expr': R_expr,
            'tok_idx': tok_idx,
            'L_text': L,
            'R_text': R,
            'used_vars': used_vars
        })

    aeval = Interpreter()
    errors = []

    # 2. 逐行校验
    for row_no, row in enumerate(rows, start=2):
        if len(row) != len(header):
            errors.append(f"第{row_no}行 列数不匹配")
            continue

        for i, rm in enumerate(rule_maps, start=1):
            aeval.symtable.clear()
            try:
                # 只给 used_vars 赋值
                for t, col in rm['tok_idx'].items():
                    var = idx_var[col]
                    if var not in rm['used_vars']:
                        continue
                    cell = row[col]
                    # 空串当 0
                    aeval.symtable[var] = float(cell) if cell else 0.0

                left_val  = aeval(rm['L_expr'])
                right_val = aeval(rm['R_expr'])
                # 判定函数
                cmpf = {
                    '==': lambda: abs(left_val - right_val) <= tol,
                    '=':  lambda: abs(left_val - right_val) <= tol,
                    '!=': lambda: abs(left_val - right_val) > tol,
                    '>=': lambda: left_val >= right_val - tol,
                    '<=': lambda: left_val <= right_val + tol,
                    '>':  lambda: left_val >  right_val + tol,
                    '<':  lambda: left_val <  right_val - tol,
                }[rm['op']]

                if not cmpf():
                    # 在提示里把列名和值都标出来
                    def highlight(s: str) -> str:
                        return re.sub(
                            r"([^\d\W][\w@\u4e00-\u9fff]*)",
                            lambda m: f"{m.group(1)}({row[rm['tok_idx'][m.group(1)]]})",
                            s, flags=re.UNICODE
                        )

                    errors.append(
                        f"第{row_no}行 规则{i} 失败："
                        f"{highlight(rm['L_text'])} {rm['op']} {highlight(rm['R_text'])}"
                    )
            except ValueError as ve:
                # 找到哪个列转换失败
                # 这里简单地把错误原样抛出
                errors.append(f"第{row_no}行 规则{i} 值转换失败：{ve}")
            except Exception as ex:
                errors.append(f"第{row_no}行 规则{i} 执行错误：{rm['orig']} → {ex}")

    return errors

# —— 示例测试 ——
if __name__ == '__main__':
    table = [
        ['商品名称', '单价', '数量', '金额', '税额'],
        ['苹果',     '10',   '2',    '20.000001', '0' ],
        ['香蕉',     '5',    '3',    '5',        '0' ],
        ['橙子',     '3',    '2',    '6',         '0' ],
    ]
    rules = ['单价 * 数量 == 金额', '数量 > 0', '税额 >= 0']
    errs = validate_table(table, rules, tol=1e-5)
    print('\n'.join(errs) or "全部通过")
