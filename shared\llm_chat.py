import openai
from typing import List, Dict, Generator

class LLMChat:
    """
    大模型对话客户端，默认无记忆，支持多轮对话、流式响应和清除记忆功能。
    """
    def __init__(
        self,
        url: str,
        key: str,
        model: str,
        temp: float = 0.7,
        think: bool = False
    ):
        """
        初始化 LLMChat 实例。

        参数:
            url (str): API 的基础 URL。
            key (str): API 密钥。
            model (str): 使用的模型名称。
            temp (float): 温度参数，控制生成文本的随机性，默认为 0.7。
            think (bool): 是否启用思考模式，默认为 False。
        """
        # 初始化客户端配置
        self.cli = openai.OpenAI(base_url=url, api_key=key)
        self.model = model
        self.temperature = temp
        self.think = think
        # 记录对话历史，用于多轮对话
        self.history: List[Dict[str, str]] = []

    def _build_messages(self, user_input: str, use_history: bool) -> List[Dict[str, str]]:
        """
        构建消息列表，可选是否包含历史。

        参数:
            user_input (str): 用户输入文本。
            use_history (bool): 是否使用历史对话。

        返回:
            List[Dict[str, str]]: 构建好的消息列表。
        """
        prefix = '' if self.think else '/no_think\n\n'
        messages = []
        if use_history:
            messages.extend(self.history)
        messages.append({"role": "user", "content": prefix + user_input})
        return messages

    def chat(self, text: str, remember: bool = False) -> str:
        """
        进行单轮或多轮对话。

        参数:
            text (str): 用户输入文本。
            remember (bool): 是否记住对话历史，默认为 False。

        返回:
            str: 模型的回复文本。
        """
        try:
            messages = self._build_messages(text, use_history=remember)
            res = self.cli.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature
            )
            reply = res.choices[0].message.content.strip()
            if remember:
                self.history.append({"role": "user", "content": text})
                self.history.append({"role": "assistant", "content": reply})
            return reply
        except Exception as e:
            return f"LLM请求失败: {e}"

    def stream_chat(self, text: str, remember: bool = False) -> Generator[str, None, None]:
        """
        进行流式对话，逐步返回响应内容。

        参数:
            text (str): 用户输入文本。
            remember (bool): 是否记住对话历史，默认为 False。

        返回:
            Generator[str, None, None]: 生成器，逐步产出响应内容。
        """
        try:
            messages = self._build_messages(text, use_history=remember)
            stream = self.cli.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
                stream=True
            )
            collected_chunks = []
            for chunk in stream:
                delta_content = getattr(chunk.choices[0].delta, "content", "")
                if delta_content is not None:
                    collected_chunks.append(delta_content)
                    yield delta_content
            full_reply = ''.join(collected_chunks).strip()
            if remember:
                self.history.append({"role": "user", "content": text})
                self.history.append({"role": "assistant", "content": full_reply})
        except Exception as e:
            yield f"LLM请求失败: {e}"

    def clear_memory(self) -> None:
        """清理聊天内存
        
        功能：
        - 清空聊天历史记录
        - 重置对话状态
        
        参数：无
        返回值：无
        """
        """
        清除对话历史，使后续请求不携带上下文。
        """
        self.history.clear()


if __name__ == "__main__":
    from config import MODEL_CONFIG
    client = LLMChat(
        url=MODEL_CONFIG['url'],
        key=MODEL_CONFIG['key'],
        model=MODEL_CONFIG['model']
    )
    # 默认单轮对话
    print(client.chat("你好，介绍一下你自己。"))
    # 显式开启多轮对话
    print(client.chat("你好，介绍一下你自己。", remember=True))
    # 流式对话示例
    for chunk in client.stream_chat("请继续。", remember=True):
        print(chunk, end='', flush=True)
    # 清除历史
    client.clear_memory()
