import re
from collections import defaultdict
from typing import List, Tuple
from shared.custom_document import CustomDocument

class FirmProcessor():
    def __call__(self, doc_bytes: bytes) -> bytes:
        doc = CustomDocument(doc_bytes)
        marked_text = doc.get_marked_text()

        # 提取所有“xxx有限公司”及其位置
        firm_pattern = r'[\u4e00-\u9fa5A-Za-z0-9（）()、·•\-]{2,}有限公司'
        matches = list(re.finditer(firm_pattern, marked_text))
        if not matches:
            return doc.get_docx_bytes()

        # 标准公司名是第一次出现的那个
        std_firm = matches[0].group()
        std_start = matches[0].start()

        # 收集公司名出现位置
        firm_occurrences = defaultdict(list)  # company_name -> list of start positions
        for match in matches:
            name = match.group()
            start = match.start()
            firm_occurrences[name].append(start)

        comments: List[Tuple[int, str]] = []

        for name, positions in firm_occurrences.items():
            if name == std_firm:
                if len(positions) >= 3:
                    # 多次出现，仅第一次标注一次
                    comments.append((positions[0], f"“{name}”在文中多次出现，请确保统一"))
                # 如果是标准名但出现不足3次，不批注
            else:
                if len(positions) < 3:
                    # 次数少于3，每次都批注
                    for pos in positions:
                        comments.append((pos, f"公司名“{name}”与标准名“{std_firm}”不一致"))
                else:
                    # 次数多于等于3，仅第一次批注
                    comments.append((positions[0], f"“{name}”与标准名“{std_firm}”不一致，且多次出现"))

        # 插入批注
        doc.insert_comment_at_position(comments, author="单位一致性审核")
        return doc.get_docx_bytes()