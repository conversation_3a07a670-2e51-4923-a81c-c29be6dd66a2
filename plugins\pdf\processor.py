from typing import List
from .pdf_extractor import PDFExtractorSimple
from .dict_comparator import DictComparator
from .docx_annotator import DocxAnnotator
from shared.llm_chat import LLMChat
from config import MODEL_CONFIG

class PdfProcessor():
    def __init__(self, pdf_bytes: List[bytes]) -> None:
        super().__init__()
        self.pdf_bytes: List[bytes] = pdf_bytes
        # 初始化 LLMChat 对象，用于后续文本处理
        self.llm = LLMChat(**MODEL_CONFIG)
        # 创建 PDFExtractorSimple 实例，用于提取 PDF 数据
        self.pdf_extractor = PDFExtractorSimple(self.llm)
        # 创建 DictComparator 实例，用于比较字段差异
        self.dict_comparator = DictComparator(self.llm)

    def __call__(self, doc_bytes: bytes) -> bytes:
        # 定义需要提取的字段列表
        fields = [
            "建设项目名称", "项目代码", "建设地点", "面积（m2）",
            "备案）文号（选填）", "总投资（万元）", "建设性质"
        ]
        
        # 第一步：从 PDF 文件中提取文本并解析指定字段
        pdf_data = self.pdf_extractor.extract(self.pdf_bytes, fields)

        # 第二步：从 Word 文档中提取字段数据
        annotator = DocxAnnotator(doc_bytes)
        word_data = {}
        for field in fields:
            value = annotator.find_next_field(field)
            if value:
                word_data[field] = value
        
        # 第三步：比较 PDF 和 Word 文档中的字段差异
        differences = self.dict_comparator.compare(pdf_data, word_data)

        # 第四步：在 Word 文档中为每个差异字段添加批注
        for key, values in differences.items():
            comment_text = f"检索附件值为: {values['value1']}, 此处为: {values['value2']}"
            annotator.annotate_field(key, comment_text, author="附件一致性审核")

        # 第五步：保存修改后的 Word 文档并返回字节数据
        return annotator.save()