import io
import zipfile
from datetime import datetime, timezone
from lxml import etree

NS = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
W = '{%s}' % NS['w']

def comment_doc_start(docx_bytes: bytes, text: str, author: str = 'Author') -> bytes:
    """在DOCX文件开头添加注释
    
    参数:
        docx_bytes: DOCX文件的字节内容
        text: 要添加的注释内容
        author: 注释作者，默认为'Author'
        
    返回值:
        bytes: 添加注释后的DOCX文件字节内容
    """
    """
    在文档开头插入批注。

    参数:
        docx_bytes (bytes): Word 文档的字节数据。
        text (str): 批注内容。
        author (str): 批注作者，默认为 'Author'。

    返回:
        bytes: 包含新批注的文档字节数据。
    """
    # 读取 docx zip 内容
    with zipfile.ZipFile(io.BytesIO(docx_bytes), 'r') as zin:
        files = {name: zin.read(name) for name in zin.namelist()}

    # 解析或新建 comments.xml
    if 'word/comments.xml' in files:
        comments = etree.fromstring(files['word/comments.xml'])
    else:
        comments = etree.Element(W + 'comments', nsmap=NS)

    # 生成新的 comment id
    existing_ids = [int(c.get(W + 'id')) for c in comments.findall('w:comment', NS)]
    cid = max(existing_ids) + 1 if existing_ids else 0

    # 创建 comment 元素
    comment = etree.SubElement(comments, W + 'comment', {
        W + 'id': str(cid),
        W + 'author': author,
        W + 'date': datetime.now(timezone.utc).isoformat()
    })
    p = etree.SubElement(comment, W + 'p')
    r = etree.SubElement(p, W + 'r')
    t = etree.SubElement(r, W + 't')
    t.text = text

    new_comments_xml = etree.tostring(comments, encoding='utf-8', xml_declaration=True, standalone='yes')

    # 解析 document.xml
    doc = etree.fromstring(files['word/document.xml'])
    body = doc.find('.//w:body', NS)

    # 找第一个段落，没有就创建
    p = body.find('w:p', NS)
    if p is None:
        p = etree.SubElement(body, W + 'p')

    # 插入一个空的 run 保证至少有一个 run
    empty_run = etree.Element(W + 'r')
    p.insert(0, empty_run)
    runs = p.findall('w:r', NS)

    # 插入 commentRangeStart
    start_r = etree.Element(W + 'r')
    start_r.append(etree.Element(W + 'commentRangeStart', {W + 'id': str(cid)}))
    p.insert(p.index(runs[0]), start_r)

    # 插入 commentRangeEnd 和 commentReference
    end_r = etree.Element(W + 'r')
    end_r.append(etree.Element(W + 'commentRangeEnd', {W + 'id': str(cid)}))
    ref_r = etree.Element(W + 'r')
    ref_r.append(etree.Element(W + 'commentReference', {W + 'id': str(cid)}))

    runs = p.findall('w:r', NS)
    p.insert(p.index(runs[-1]) + 1, end_r)
    p.insert(p.index(runs[-1]) + 2, ref_r)

    new_doc_xml = etree.tostring(doc, encoding='utf-8', xml_declaration=True, standalone='yes')

    # 打包回 docx bytes
    out_io = io.BytesIO()
    with zipfile.ZipFile(out_io, 'w', zipfile.ZIP_DEFLATED) as zout:
        for name, data in files.items():
            if name == 'word/comments.xml':
                zout.writestr(name, new_comments_xml)
            elif name == 'word/document.xml':
                zout.writestr(name, new_doc_xml)
            else:
                zout.writestr(name, data)
        if 'word/comments.xml' not in files:
            zout.writestr('word/comments.xml', new_comments_xml)

    return out_io.getvalue()

# 测试调用示例
if __name__ == '__main__':
    with open('input.docx', 'rb') as f:
        data = f.read()
    out_data = comment_doc_start(data, '这是批注内容', author='Ken')
    with open('out.docx', 'wb') as f:
        f.write(out_data)
    print('批注插入完成，输出out.docx')
