from typing import List, Optional, Callable
from utils import remove_docx_comments

class Workflow:
    """
    文档处理工作流类，用于按顺序执行多个处理步骤。
    """
    def __init__(self, steps: List[callable[[bytes],bytes]]):
        """
        初始化 Workflow 实例。

        参数:
            steps (List[callable[[bytes],bytes]]): 处理步骤列表，每个步骤都是一个可调用对象。
        """
        self.steps = steps

    def run(
        self,
        input_bytes: bytes,
        progress_callback: Optional[Callable[[str], None]] = None
    ) -> bytes:
        """
        执行工作流，按顺序处理文档。

        参数:
            input_bytes (bytes): 输入的文档字节数据。
            progress_callback (Optional[Callable[[str], None]]): 进度回调函数，默认为 None。

        返回:
            bytes: 处理后的文档字节数据。
        """
        def report(msg: str):
            if progress_callback:
                progress_callback(msg)
            else:
                print(msg)

        total_steps = len(self.steps) + 1  # CHANGED: 移除了“合并批注”这一步
        step_num = 1

        # Step 1：清理注释
        report(f"[Step {step_num}/{total_steps}] 清理注释...开始")
        data = remove_docx_comments(input_bytes)
        report(f"[Step {step_num}/{total_steps}] 清理注释...完成")
        step_num += 1

        # 串行执行插件，每一步使用上一步的输出
        for step in self.steps:
            report(f"[Step {step_num}/{total_steps}] 插件 {step.name} 处理...开始")
            data = step.process(data)
            report(f"[Step {step_num}/{total_steps}] 插件 {step.name} 处理...完成")
            step_num += 1

        return data  # CHANGED: 直接返回最终处理后的数据


if __name__ == "__main__":
    import argparse
    from config import DOC_FORMAT
    from . import TitleProcessor  # 根据实际路径调整

    parser = argparse.ArgumentParser(description="DOCX 审核工作流处理器")
    parser.add_argument(
        "input",
        nargs="?",
        default="input.docx",
        help="输入 DOCX 文件路径，默认 input.docx"
    )
    parser.add_argument(
        "output",
        nargs="?",
        default="output.docx",
        help="输出 DOCX 文件路径，默认 output.docx"
    )
    args = parser.parse_args()

    def print_progress(msg: str):
        """打印进度信息
        
        参数:
            msg: 要打印的进度信息
            
        返回值: 无
        """
        print(msg)

    # 示例：仅 TitleProcessor
    workflow = Workflow([TitleProcessor(DOC_FORMAT)])

    with open(args.input, "rb") as f:
        input_bytes = f.read()

    output_bytes = workflow.run(
        input_bytes,
        progress_callback=print_progress
    )

    with open(args.output, "wb") as f:
        f.write(output_bytes)
