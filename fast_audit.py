from typing import List, Callable
import streamlit as st
import time

from plugins.ban import BanProcessor
from plugins.firm import FirmProcessor
from plugins.material import MaterialProcessor
from plugins.site import SiteProcessor
from plugins.tabulate import TabulateProcessor
from plugins.title import TitleProcessor


PLUGINS=[BanProcessor(),FirmProcessor(),MaterialProcessor(),SiteProcessor(),TabulateProcessor(),TitleProcessor()]

def fast_audit(plugins: List[Callable[[bytes], bytes]] = PLUGINS):
    st.title("🚀 一键审阅")
    st.markdown("上传 .docx 文档后，系统会依次调用所有插件进行处理，最后产出审阅结果。")

    uploaded_file = st.file_uploader("上传文档 (.docx)", type=["docx"])
    if not uploaded_file:
        return  # 未上传就不往下执行

    # 当用户点击“开始审阅”按钮时触发
    if st.button("开始审阅", use_container_width=True):
        with st.spinner("审阅中，请稍候..."):
            # 1) 读取上传内容
            doc_bytes = uploaded_file.read()

            # 2) 依次调用插件
            for plugin in plugins:
                try:
                    doc_bytes = plugin(doc_bytes)
                except Exception as e:
                    st.error(f"插件 {plugin.__name__} 出错：{e}")
                    return

            # 3) 小延迟以保证 spinner 能显示出来
            time.sleep(0.5)

        st.success("审阅完成！")

        # 4) 提供下载
        st.download_button(
            label="⬇️ 下载审阅结果",
            data=doc_bytes,
            file_name=f"审阅结果_{uploaded_file.name}",
            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            use_container_width=True
        )
