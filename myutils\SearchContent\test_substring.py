def find_in_first_not_in_second_substring(arr1, arr2):
    """
    找出在数组arr1中但不在数组arr2中任何字符串作为子字符串出现的字符串。
    例如，"石" 在arr2中有 "石子" 作为父字符串时，不视为缺失。
    
    参数:
    arr1: list of str, 第一个字符串数组
    arr2: list of str, 第二个字符串数组
    
    返回:
    list of str: 在arr1中且不是arr2中任何字符串的子字符串的字符串列表。
    """
    # 遍历arr1，仅当元素不是arr2中任何字符串的子字符串时才保留
    result = [s for s in arr1 if not any(s in t for t in arr2)]
    return result

'''
def find_in_second_not_in_first_substring(arr1, arr2):
    """
    找出在数组arr2中但不在数组arr1中任何字符串作为子字符串出现的字符串。
    
    参数:
    arr1: list of str, 第一个字符串数组
    arr2: list of str, 第二个字符串数组
    
    返回:
    list of str: 在arr2中且不是arr1中任何字符串的子字符串的字符串列表。
    """
    result = [s for s in arr2 if not any(s in t for t in arr1)]
    return result
'''

def find_in_second_not_in_first(arr1, arr2):
    """
    找出在数组arr2中但不在数组arr1中的字符串（基于字符级包含关系）。
    条件：arr2中的字符串s，若s的每个字符都出现在arr1中任意字符串的字符集合里，则排除。
    
    参数:
    arr1: list of str, 第一个字符串数组
    arr2: list of str, 第二个字符串数组
    
    返回:
    list of str: 在arr2中且至少包含一个arr1字符集合中不存在的字符的字符串列表。
    """
    import re
    
    # 分割函数：按顿号或斜杠分割字符串
    def split_string(s):
        # 确保输入是字符串类型
        if not isinstance(s, str):
            # 如果不是字符串，尝试转换为字符串
            if s is None:
                return []
            s = str(s)

        # 使用正则表达式按顿号（、）或斜杠（/）分割
        parts = re.split(r'[、/]', s)
        # 过滤空字符串并去除首尾空格
        return [part.strip() for part in parts if part.strip()]
    
    # 对arr1和arr2中的字符串进行分割
    split_arr1 = []
    for s in arr1:
        # 确保s不是None或空值
        if s is not None:
            split_arr1.extend(split_string(s))

    split_arr2 = []
    for s in arr2:
        # 确保s不是None或空值
        if s is not None:
            split_arr2.extend(split_string(s))
    
    # 构建split_arr1的字符集合（包含所有字符串的所有字符）
    charset1 = set()
    for s in split_arr1:
        for char in s:
            charset1.add(char)
    
    # 检查split_arr2中字符串：若存在字符不在charset1中，则保留
    result = []
    for s in split_arr2:
        for char in s:
            if char not in charset1:
                result.append(s)
                break  # 发现一个不存在的字符即可跳过剩余检查
    return result

if __name__ == "__main__":
    arr1 = ["水泥、石英砂、天然砂/", "中砂/机制砂、陶粒、石", "子、再生集料"]
    arr2 = ["水泥", "石英砂","天然砂/中砂/机制砂","陶粒/石子/再生集料"]

    #result1_sub = find_in_first_not_in_second_substring(arr1, arr2)
    result2_sub = find_in_second_not_in_first(arr1, arr2)

    #print("数组1中2没有的字符串（子字符串检查）:", result1_sub)  # 输出: ['石英']（因为"石"和"子"是"石子"的子字符串）
    print("数组2中1没有的字符串（子字符串检查）:", result2_sub)  # 输出: ['陶瓷']（因为"石子"是"石"和"子"的组合，所以不视为缺失）
