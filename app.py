import streamlit as st
from pathlib import Path
import sys
import time

# --------------------------------------------------
# streamlit config
# --------------------------------------------------
st.set_page_config(
    page_title="环评文档审核系统",
    layout="centered",  # 主区域居中布局
    initial_sidebar_state="expanded"
)

# --------------------------------------------------
# Global styling for sidebar and main area
# --------------------------------------------------
st.markdown(
    """
    <style>
      /* 侧边栏按钮样式 */
      section[data-testid="stSidebar"] button {
        width: 100% !important;
        margin: 4px 0 !important;
        border-radius: 8px !important;
        text-align: left !important;
      }

      /* 侧边栏宽度 */
      section[data-testid="stSidebar"] {
        min-width: 260px !important;
      }

      /* 主区域最大宽度和居中 */
      [data-testid="stAppViewContainer"] > .main > [data-testid="stBlock"] {
        max-width: 800px;
        margin: 0 auto;
        padding: 1rem 2rem;
      }
    </style>
    """,
    unsafe_allow_html=True
)


# --------------------------------------------------
# Page definitions
# --------------------------------------------------
def home():
    st.markdown("# 📋 环评文档审核系统")
    st.markdown("""
    这是一个智能文档审核系统，支持以下功能：
    - 多源异构附件数据解析与校验
    - 文档结构与要素一致性校验
    - 图文及表格抽取与语义一致性校验

    请从侧边栏选择“文档审核”页面开始使用。
    """)


def one_click_audit():
    st.title("🚀 一键审阅")
    file = st.file_uploader("上传文档(.docx)")
    if file and st.button("开始审阅", use_container_width=True):
        with st.spinner("分析中..."):
            time.sleep(100)
        st.success("审阅完成！")

# --------------------------------------------------
# Dynamic page loader
# --------------------------------------------------
def load_page(path):
    p = Path(path)
    if not p.exists():
        st.error(f"找不到页面: {path}")
        return
    roots = [str(p.parent.parent), str(p.parent)]
    for r in roots:
        sys.path.insert(0, r)
    try:
        code = p.read_text(encoding='utf-8')
        exec(code, {'st': st, '__name__': '__main__'})
    except Exception as e:
        st.error(e)
    finally:
        for r in roots:
            if r in sys.path:
                sys.path.remove(r)

# --------------------------------------------------
# Build pages dictionary grouped by folder
# --------------------------------------------------
pages = {}
for f in sorted(Path("view").rglob("*.py")):
    grp = f.relative_to("view").parent.name or "其他"
    pages.setdefault(grp, []).append({'title': f.stem, 'path': str(f)})

# --------------------------------------------------
# Initialize session state
# --------------------------------------------------
if 'sel' not in st.session_state:
    st.session_state.sel = 'home'

# --------------------------------------------------
# Sidebar navigation with expanders per folder
# --------------------------------------------------
with st.sidebar:
    st.header("📋 环评文档审核系统")
    # Home & One-Click Audit
    if st.button("🏠 首页", use_container_width=True):
        st.session_state.sel = 'home'
    if st.button("🚀 一键审阅", use_container_width=True):
        st.session_state.sel = 'audit'
    st.markdown("---")

    # Section expanders for each group
    for grp, items in pages.items():
        with st.expander(f"📂 {grp}", expanded=False):
            for it in items:
                if st.button(it['title'], use_container_width=True, key=it['path']):
                    st.session_state.sel = it['path']

# --------------------------------------------------
# Main content rendering based on selection
# --------------------------------------------------
sel = st.session_state.sel
if sel == 'home':
    home()
elif sel == 'audit':
    from fast_audit import fast_audit
    fast_audit()
elif any(sel == it['path'] for group in pages.values() for it in group):
    load_page(sel)
else:
    home()