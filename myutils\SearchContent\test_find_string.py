if __name__ == "__main__":
    arr1 = ['水泥', '石英砂', '天然砂/中砂/机制砂', '陶粒/石子/再生集料', '水', '颜料']
    arr2 = ['水泥、石英砂、天然砂', '中砂/机制砂、陶粒、石', '子、再生集料', 'G1-1装卸粉尘、', '原料储备', 'G1-2商仓粉尘', '水', '配料、搅拌', 'G1-3投料粉尘', '二', '水', '制品成型', 'T', '电供热', '制品养护', '深加工', 'G1-4抛丸粉尘', '成品码垛', 'S1-1残渣', '成品']
    
    import sys
    import os
    # 获取当前文件所在目录的父目录（DOCTEST目录）
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.append(project_root)  # 添加到Python路径
    from SearchContent.substringtest import find_in_first_not_in_second_substring
    def replace_symbols(strings):
        """
        将字符串数组中的顿号（、）和斜杠（/ 和 \）替换为空格
        
        参数:
            strings: list[str], 待处理的字符串数组
            
        返回:
            list[str]: 处理后的字符串数组，其中顿号和斜杠已被替换为空格
        """
        # 创建字符映射表：顿号、正斜杠、反斜杠 → 空格
        trans_table = str.maketrans({
            '、': ' ',  # 中文顿号
            '/': ' ',   # 正斜杠
            '\\': ' '   # 反斜杠（需双写转义）
        })
        
        # 应用映射表到每个字符串
        return [s.translate(trans_table) for s in strings]
    related_img_text=replace_symbols(arr2)
    matierials=replace_symbols(arr1)
    graph_related_matierials=find_in_first_not_in_second_substring(related_img_text,matierials)

    print(graph_related_matierials)

