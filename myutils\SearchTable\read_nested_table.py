'''
import docx

def read_nested_table(table):
    nested_tables = []

    for row in table.rows:
        for cell in row.cells:
            # 判断单元格是否包含嵌套表格
            if cell.tables:
                for table in cell.tables:
                    nested_tables.append(table)
    
    return nested_tables

def are_all_elements_in_source(target_list, source_list):
    # 将source_list转换为集合以提高查找效率
    #source_set = set(source_list)
    # 遍历target_list中的每个元素
    for element in target_list:
        # 如果当前元素不在source_set中，立即返回False
        if element not in source_list:
            return False
    # 所有元素都在source_list中，返回True
    return True

# 打开Word文档
doc = docx.Document('input/sample5.docx')

# 读取文档中的所有表格
tables = doc.tables
all_table_data=[]
# 遍历所有表格
for table in tables:
    # 处理当前表格
    nested_tables = read_nested_table(table)
    
    # 打印当前表格的内容
    
    for row in table.rows:
        for cell in row.cells:
            print(cell.text)
    
    
    # 打印嵌套表格的内容
    
    for nested_table in nested_tables:
        for nested_row in nested_table.rows:
            for nested_cell in nested_row.cells:
                print(nested_cell.text)
    
    
    tables_data = [] #将最后的保存结果存入到列表中
    for table in nested_tables:
        table_data = []
        for row in table.rows:
            row_data = [cell.text for cell in row.cells]
            table_data.append(row_data)
        tables_data.append(table_data)
    
    #print(tables_data)
    all_table_data.append(tables_data)

#print(all_table_data)
#print(len(all_table_data))

#table_columns=['序号','名称','成分/形态','年用量']

for tables_data in all_table_data:
    for table_data in tables_data:
        #print(table_data)
        if(len(table_data)==0):
            continue
        table_data_0=(table_data[0])
        #print(table_data_0)
        if('序号' in table_data_0 and '名称' in table_data_0 and '成分/形态' in table_data_0 ):
            print(table_data)
'''

from docx import Document

def extract_tables(doc_bytes,headers=["原料名称","原料名称"]):
    """
    从Word文档中提取满足特定表头条件的嵌套表格数据
    
    参数:
        docx_path (str): Word文档路径
        
    返回:
        list: 包含所有满足条件的表格数据的列表(三维列表: [表格][行][单元格])
    """
    def read_nested_table(table):
        """提取表格中的所有嵌套表格"""
        nested_tables = []
        nested_text=""
        for row in table.rows:
            for cell in row.cells:
                if cell.tables:
                    for nested_table in cell.tables:
                        nested_tables.append(nested_table)
                if cell.text:
                    nested_text+=cell.text.strip()
                    
        return (nested_tables,nested_text)

    # 打开Word文档
    #doc = Document(docx_path)
    import io
    try:
        doc_stream =io.BytesIO(doc_bytes)  # 字节流转换为内存流
        doc = Document(doc_stream)  # 加载为Document对象
    except Exception as e:
        raise ValueError("文件格式错误或非.docx文件，请检查输入是否为有效的.docx字节流") from e
    tables = doc.tables
    all_table_data = []
    
    # 遍历所有顶层表格
    for table in tables:
        # 获取当前表格中的所有嵌套表格
        nested_tables,nested_text = read_nested_table(table)

        if ("主要原辅料" in nested_text or "主要原辅材料" in nested_text) and "表" in nested_text:
        
            # 处理每个嵌套表格
            tables_data = []
            for nested_table in nested_tables:
                table_data = []
                for row in nested_table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                tables_data.append(table_data)
            
            all_table_data.append(tables_data)
    
    
    # 筛选满足条件的表格数据
    result_tables = []
    for tables_data in all_table_data:
        for table_data in tables_data:
            if not table_data:  # 跳过空表格
                continue
            header_row = table_data[0]  # 获取表格的第一行作为表头
            # 检查表头是否包含特定字段
            #break_flag=True
            #for header in headers:
            #    if header not in header_row:
             #       break_flag=False

            if header_row[0]==header_row[1] and header_row[0]=="原料名称":
                result_tables.append(table_data)
    
    return result_tables

def extract_process_materail_relations(table):
    col_datas=[]
    for col_idx in range(2):  # 每个表读前两列
        col_data = [row[col_idx] for row in table]
        col_data=col_data[1:]
        #col_data=list(dict.fromkeys(col_data))
        col_datas.append(col_data)
        print(f"表第{col_idx}列:", col_data)

    #process_names=list(set(col_datas[0]))
    process_names=list(dict.fromkeys(col_datas[0]))
    print("工艺名称：",process_names)
    
    process_materail_relations=[]
    for process_name in process_names:
        material_names=[]
        for i in range(len(col_datas[0])):
            if col_datas[0][i]==process_name:
                material_names.append(col_datas[1][i])
        material_names=list(dict.fromkeys(material_names))
        print(f"工艺{process_name}对应的原料列表：",material_names)
        process_materail_relation=(process_name,material_names)
        process_materail_relations.append(process_materail_relation)
    
    return process_materail_relations

        
        
   

# 使用示例
if __name__ == "__main__":
    # 提取指定文档中的表格数据
    #headers=['序号','名称','成分/形态']
    #extracted_data = extract_tables('input/sample5.docx',headers)
    doc_path = "input/input.docx"
    with open(doc_path, "rb") as file:
        word_bytes = file.read()
        extracted_data = extract_tables(word_bytes)
    
    # 打印提取结果
        for i, table in enumerate(extracted_data):
            print(f"表格 {i+1}:")
            for row in table:
                print(row)
            print("-" * 40)


        process_materail_relations=extract_process_materail_relations(extracted_data[0])
        print("工艺与原料关系：",process_materail_relations)
        
        