import re
from typing import Iterable
from shared.custom_document import CustomDocument
from .config import BLA<PERSON><PERSON>, WHITE

class BanProcessor():
    def __init__(self, blacklist: Iterable[str] = BLACK, whitelist: Iterable[str] = WHITE):
        self.whitelist = sorted(set(whitelist), key=len, reverse=True)
        self.blacklist = sorted(set(blacklist), key=len, reverse=True)

    def __call__(self, doc_bytes: bytes) -> bytes:
        doc = CustomDocument(doc_bytes)
        text = doc.get_marked_text()
        mask = [False] * len(text)
        comments = []

        def mark(words, add_comment=False):
            for word in words:
                for m in re.finditer(re.escape(word), text):
                    start, end = m.start(), m.end()
                    if any(mask[start:end]): continue
                    mask[start:end] = [True] * (end - start)
                    if add_comment:
                        comments.append((start, f"违禁品：{word}"))

        mark(self.whitelist)
        mark(self.blacklist, add_comment=True)
        doc.insert_comment_at_position(comments, author="违禁品审查")
        return doc.get_docx_bytes()
