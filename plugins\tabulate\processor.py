from .extract_named_table import extract_named_table
from .merge_header import merge_header
from .validate_table import validate_table
from utils.comment_doc_start import comment_doc_start
from utils.comment_first import comment_first

class TabulateProcessor():
    def __call__(self, doc_bytes: bytes) -> bytes:
        table_rules = {
            "建设项目有组织废气污染物产排一览表":[
                "有组织产生量@产生量 = 产生量 * 收集效率 / 100",
                "有组织产生量@产生量 = 有组织产生量@速率 * 排放时间 / 1000",
                "有组织产生量@浓度 = 有组织产生量@速率 * 1000000 / 废气量",
                "排放情况@浓度 = 有组织产生量@浓度 * ( 100 - 治理措施@效率 ) / 100",
                "排放情况@速率 = 有组织产生量@速率 * ( 100 - 治理措施@效率 ) / 100",
                "排放情况@产生量 = 有组织产生量@产生量 * ( 100 - 治理措施@效率 ) / 100",
                "排放标准@浓度 >= 排放情况@浓度",
                "排放标准@速率 >= 排放情况@速率",
            ],
            "无组织污染物产生及排放状况":["产生量 = 排放速率 * 排放时间 / 1000"],
            "排气筒基本参数表":["排风量 = ( 直径 * 直径 * 3.1415 / 4 ) * ( 烟气流速 * 3600 ) * 273.15 / ( 温度 + 273.15 )"],
            "本项目废水产生情况表":[
                "产生量 = 废水量 * 产生浓度 / 1000000",
                "接管排放量 = 废水量 * 接管浓度 / 1000000",
                "最终排放量 = 废水量 * 最终排放浓度 / 1000000"
            ]
        }

        for name, rules in table_rules.items():
            table = merge_header(extract_named_table(doc_bytes, name))

            if not table:
                doc_bytes = comment_doc_start(doc_bytes, f'未找到"{name}"表格', author="表格计算")
            else:
                result = validate_table(table,rules)
                if result:
                    doc_bytes = comment_first(doc_bytes, name, '\n'.join(result))
        
        return doc_bytes