import os
import zipfile
from datetime import datetime
from pathlib import Path
import fnmatch

# ========== 配置 ==========
PROJECT_DIR = Path(".")                      # 要打包的项目目录
OUTPUT_DIR = Path("./output")                # 压缩包输出目录
ARCHIVE_NAME_PREFIX = "AUDITOR"              # 压缩包文件名前缀

# 内置排除规则（目录名、文件名、通配符）
EXCLUDE_PATTERNS = [
    "input", "output",                       # 输入/输出目录
    "__pycache__", "*.pyc", "*.pyo", "*.pyd", "*$py.class",  # Python 缓存
    "venv", "env", ".env",                   # 虚拟环境
    ".vscode", ".idea",                      # IDE 配置
    "*.log", "*.tmp", "*.temp",              # 临时/日志文件
    ".DS_Store", "Thumbs.db",                # 系统文件
    "node_modules",                          # Node.js 依赖
    "*.bak", "*.swp", "*.swo", "*~",         # 其他临时文件
    ".local", "site-packages", "dist-info", "egg-info"  # 额外排除 Python 环境和包文件夹
]
# ===========================

def should_exclude(relative_path: Path):
    """
    判断某个文件或目录的相对路径是否应被排除。
    支持目录名完全匹配和文件名通配符匹配。
    """
    parts = relative_path.parts
    for pattern in EXCLUDE_PATTERNS:
        # 先检查目录名是否匹配（只匹配最顶层目录名）
        if pattern in parts:
            return True
        # 再用fnmatch匹配文件名
        if fnmatch.fnmatch(relative_path.name, pattern):
            return True
        # 也尝试用fnmatch匹配整个相对路径（如通配符匹配子目录文件）
        if fnmatch.fnmatch(str(relative_path), pattern):
            return True
    return False

def create_zip(project_dir: Path, output_dir: Path, prefix: str):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_name = f"{prefix}_{timestamp}.zip"
    archive_path = output_dir / archive_name
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"开始打包目录：{project_dir.resolve()}")
    total_files = 0
    with zipfile.ZipFile(archive_path, "w", compression=zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
        for root, dirs, files in os.walk(project_dir):
            root_path = Path(root)
            relative_root = root_path.relative_to(project_dir)

            # 过滤要进入的子目录，防止进入排除目录
            dirs[:] = [d for d in dirs if not should_exclude(relative_root / d)]

            print(f"📂 扫描目录: {relative_root if str(relative_root) != '.' else project_dir.name}")

            added_in_dir = 0
            for file in files:
                file_path = root_path / file
                relative_file = relative_root / file
                if should_exclude(relative_file):
                    continue
                zipf.write(file_path, relative_file)
                added_in_dir += 1
                total_files += 1
            if added_in_dir > 0:
                print(f"  - {relative_root} 添加了 {added_in_dir} 个文件")

    print(f"✅ 打包完成：{archive_path}，共 {total_files} 个文件。")

if __name__ == "__main__":
    create_zip(PROJECT_DIR, OUTPUT_DIR, ARCHIVE_NAME_PREFIX)
