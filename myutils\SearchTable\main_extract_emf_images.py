from docx import Document
from PIL import Image
#import pyemf
import re
import json
from io import BytesIO

import sys
import os


# 获取当前文件所在目录的父目录（myutils目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
from TONGQ.extract_named_table import extract_named_table
from OCRTest.targetocrtest import test_ocr_function

def extract_emf_images(doc_bytes, output_dir):
    # 创建输出目录
    import os
    import datetime
    # Get current date and time
    now = datetime.datetime.now()
    formatted_time = now.strftime('%Y-%m-%d-%H-%M-%S')
    print("Formatted Date and Time:", formatted_time)
    output_dir = output_dir  + formatted_time
    os.makedirs(output_dir, exist_ok=True)
    try:
        doc_stream = BytesIO(doc_bytes)  # 字节流转换为内存流
        doc = Document(doc_stream)  # 加载为Document对象
    except Exception as e:
        raise ValueError("文件格式错误或非.docx文件，请检查输入是否为有效的.docx字节流") from e
    relations = []
    
    # 正则匹配图片ID
    pattern = re.compile(r'rId(\d+)')
    
    # 遍历表格
    for table_idx, table in enumerate(doc.tables):
        for row_idx, row in enumerate(table.rows):
            
            for cell_idx, cell in enumerate(row.cells):
                
                ns = {'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'}
                text_parts = []
                textboxes = cell._element.findall('.//w:txbxContent', ns)
                for textbox in textboxes:
                    
                    # 提取文本框内所有段落文本
                    for paragraph in textbox.findall('.//w:p', ns):
                        # 拼接段落内所有文本片段
                        para_text = ''.join(run.text for run in paragraph.findall('.//w:t', ns))
                        text_parts.append(para_text)

                count=0
                cell_text = cell.text.strip()
                
                # 检查单元格内图片
                for paragraph in cell.paragraphs:
                   
                    for run in paragraph.runs:
                        # 通过XML解析定位EMF图片
                        match = pattern.search(run._element.xml)
                        if match:
                            rId = f'rId{match.group(1)}'
                            
                            # 提取图片二进制数据
                            rel = doc.part.rels[rId]
                            if "image" in rel.target_ref:
                                if rel.target_ref.endswith('.emf'):
                                    # EMF图片处理
                                    emf_data = rel.target_part.blob
                                    
                                    # 保存原始EMF文件
                                    emf_path = os.path.join(output_dir, f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.emf')
                                    count += 1
                                    with open(emf_path, 'wb') as f:
                                        f.write(emf_data)
                                    
                                    # 转换为PNG
                                    #png_path = emf_path.replace('.emf', '.png')

                                    import sys
                                    import os
                                    # 获取当前文件所在目录的父目录（DOCTEST目录）
                                    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                                    sys.path.append(project_root)  # 添加到Python路径
                                    from SearchContent.emf_to_png_converter import convert_emf_file_to_png

                                    #convert_emf_to_png(emf_data, output_dir, emf_path)
                                    png_path = convert_emf_file_to_png(emf_path, dpi=250)
                                    
                                    # 建立关系映射
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": emf_path,
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                    })
                                    
                                elif rel.target_ref.endswith('.png'):
                                    # PNG图片处理
                                    png_data = rel.target_part.blob
                                    
                                    # 直接保存PNG文件
                                    png_path = os.path.join(output_dir, f'table{table_idx}_row{row_idx}_cell{cell_idx}_{count}.png')
                                    count += 1
                                    with open(png_path, 'wb') as f:
                                        f.write(png_data)
                                    
                                    # 建立关系映射
                                    relations.append({
                                        "table_index": table_idx,
                                        "row": row_idx,
                                        "cell": cell_idx,
                                        "cell_text": cell_text,
                                        "emf_path": None,  # 没有对应的EMF文件
                                        "png_path": png_path,
                                        "text_parts": text_parts,
                                    })
    # 保存关系数据
    with open(os.path.join(output_dir, 'relations.json'), 'w') as f:
        json.dump(relations, f, indent=2)
    
    return relations



# 辅助函数
def search_content_in_list(content_list, content):
    """在列表中搜索内容"""
    for item in content_list:
        if content in item:
            return True
    return False

def has_brackets(text):
    """判断字符串中是否包含括号
    
    参数:
        text (str): 要检查的字符串
        
    返回:
        bool: 如果字符串中包含()、[]或{}等括号则返回True，否则返回False
    """
    brackets = ['(', ')', '（','）','[', ']', '{', '}']
    return any(bracket in text for bracket in brackets)

def search_content_in_text(text, content):
    """在文本中搜索内容"""
    if content in text:
        return True
    return False

def setup_project_paths():
    """设置项目路径"""
    import sys
    import os
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.append(project_root)
    return project_root

def extract_and_display_relations(doc_bytes):
    """提取并显示EMF图像关系"""
    relations = extract_emf_images(doc_bytes, "output/images/")
    print("所有表格信息：")
    for rel in relations:
        print(f"表格{rel['table_index']}的单元格({rel['row']},{rel['cell']})")
        print(f"  EMF路径: {rel['emf_path']}")
        print(f"  PNG路径: {rel['png_path']}\n")
        print(f"  单元格文本: {rel['cell_text']}\n")
        print(f"  文本框片段: {rel['text_parts']}\n")
    return relations

def extract_and_display_tables(doc_bytes):
    """提取并显示表格数据"""
    from SearchTable.read_nested_table import extract_tables, extract_process_materail_relations

    extracted_data = extract_tables(doc_bytes)
    print("已提取的表格数据：")
    for i, table in enumerate(extracted_data):
        print(f"表格 {i+1}:")
        for j, row in enumerate(table):
            if j != 0:
                print(row)
        print("-" * 40)

    process_materail_relations = extract_process_materail_relations(extracted_data[0])
    print("工艺与原料关系：", process_materail_relations)

    return extracted_data, process_materail_relations

def find_related_images(relations, graph_name):
    """查找相关图片"""
    related_imgs = []
    search_content = graph_name
    for rel in relations:
        if (search_content_in_list(rel['text_parts'], search_content) or
            search_content_in_list(rel['cell_text'].split("\n"), search_content)):
            related_imgs.append(rel['png_path'])
    print("png图片地址：", related_imgs)
    return related_imgs

def process_image_ocr(related_imgs):
    """处理图片OCR识别"""
    from OCRTest.paddleocrtest import ocr_cpu

    related_img_texts = []
    for related_img in related_imgs:
        #texts = ocr_cpu(related_img)
        #if (search_content_in_list(texts, "原料") or
        #    search_content_in_list(texts, "原材料") or
        #    search_content_in_list(texts, "下料")):
        #    related_img_texts.append(texts)
        _,_,extracted_texts = test_ocr_function(related_img)
        related_img_texts.append(extracted_texts)
    print("图片识别结果：")
    for img_text in related_img_texts:
        print(img_text)
    return related_img_texts

def extract_related_texts(relations, graph_name):
    """提取相关文本"""
    related_texts = []
    search_content = graph_name
    for rel in relations:
        if (search_content_in_list(rel['text_parts'], search_content) or
            search_content_in_list(rel['cell_text'], search_content)):
            related_texts.append(rel['cell_text'])
        if (search_content_in_text(rel['text_parts'], search_content) or
            search_content_in_text(rel['cell_text'], search_content)):
            related_texts.append(rel['cell_text'])
    related_texts = list(set(related_texts))
    print("相关文字说明:", related_texts)
    return related_texts

def extract_related_text_parts(relations, graph_name):
    """提取相关文本片段"""
    related_text_parts = []
    search_content = graph_name
    for rel in relations:
        if (search_content_in_list(rel['text_parts'], search_content) or
            search_content_in_list(rel['cell_text'], search_content)):
            related_text_parts.extend(rel['text_parts'])
        if (search_content_in_text(rel['text_parts'], search_content) or
            search_content_in_text(rel['cell_text'], search_content)):
            related_text_parts.extend(rel['text_parts'])
    print("相关文字框:", related_text_parts)
    return related_text_parts

def segment_text_content(related_texts, process_materail_relations):
    """分段文本内容"""
    related_texts_content = ""
    for related_text in related_texts:
        related_texts_content = related_texts_content + related_text

    text_split_keywords = []
    for process_material_relation in process_materail_relations:
        process, materials = process_material_relation
        text_split_keywords.append(process + "生产工艺流程")
    print("分段关键词为：", text_split_keywords)
    number_of_keywords = len(text_split_keywords)
    print("分段关键词数量为：", number_of_keywords)
    print("原始文本内容长度：", len(related_texts_content))
    print("原始文本内容预览：", related_texts_content[:200] + "...")

    
    realted_text_segments = [];parseFlag=True
    if len(text_split_keywords) == 1:
        realted_text_segments.append(related_texts_content)
        print("仅包含一个关键词，将直接返回所有文本内容")
    else:
        try:
           
     
            split_content = related_texts_content
            for i, keyword in enumerate(text_split_keywords):
                parts = split_content.split(keyword)
                print(f"第{i+1}个关键词：{keyword}")
                print(f"分割为{len(parts)}个部分")
                print(f"分割后的结果：{parts}")
                if len(parts) > 1:
                    realted_text_segments.append(parts[-2])
                    split_content = parts[-1]
                    if i == len(text_split_keywords) - 1:
                        realted_text_segments.append(parts[-1])            
            if len(realted_text_segments) > 0:
                realted_text_segments = realted_text_segments[1:]
            else:
                parseFlag = False
            
        except:
            parseFlag = False
            print("初次识别失败，正在尝试第二次识别...")
    
    if not parseFlag:
        keyword="工艺流程"
        print(f"尝试识别 {keyword} 关键字...")
        split_content = related_texts_content
        parts = split_content.split(keyword)
        print(f"分段数: {len(parts)}")
        for i,part in enumerate(parts):        
            print(f"第{i}段，分段内容: {part}")
        if len(parts) > 1:
            realted_text_segments.extend(parts)
            if len(realted_text_segments)==len(text_split_keywords)+1:
                realted_text_segments = realted_text_segments[1:]       
    

    print("分段结果:", realted_text_segments)
    for i, segment in enumerate(realted_text_segments):
        print(f"第{i}段：")
        print(segment)

    return related_texts_content, realted_text_segments

def add_comment_on_matrial(doc_bytes, material_table_name="主要原辅料情况表"):
    """主函数：为材料表添加注释"""
    # 设置项目路径
    setup_project_paths()

    # 提取EMF图像关系
    relations = extract_and_display_relations(doc_bytes)

    # 提取表格数据
    graph_name_keywords=[]
    _, process_materail_relations = extract_and_display_tables(doc_bytes)

    


    # 获取主要工艺流程名称
    for process_name, process_relations in process_materail_relations:
    
        graph_name_keywords.append(process_name)

    graph_name=find_graph_name_by_keywords(relations,graph_name_keywords)
    print("工艺流程图名：",graph_name)

    # 提取相关文本
    related_texts = extract_related_texts(relations, graph_name)
    related_text_parts = extract_related_text_parts(relations, graph_name)

    # 分段文本内容
    related_texts_content, realted_text_segments = segment_text_content(related_texts, process_materail_relations)

    # 检查realted_text_segments是否为空
    if not realted_text_segments or len(realted_text_segments) == 0:
        print("错误：realted_text_segments为空，无法继续处理")
        sys.exit(1)

    # 查找相关图片
    related_imgs = find_related_images(relations, graph_name)

    # 处理图片OCR
    related_img_texts = process_image_ocr(related_imgs)

   

    # 检查材料在图片和文本中的存在情况（按表找图）
    comment_issues1 = check_materials_in_images_and_text(relations,process_materail_relations, related_img_texts, realted_text_segments, material_table_name)

    # 检查图片中的材料在材料表中的存在情况（按图找表）
    comment_issues2 = check_images_in_materials_table(process_materail_relations, related_img_texts, related_texts_content, related_text_parts)
    
    comment_issues3=check_equipment_in_related_text(doc_bytes,related_texts_content)
    # 合并所有注释问题
    all_comment_issues = comment_issues1 + comment_issues2+comment_issues3

    # 保存带有注释的文档
    word_bytes = save_document_with_comments(doc_bytes, all_comment_issues)

    return word_bytes

def check_materials_in_images_and_text(relations,process_materail_relations, related_img_texts, realted_text_segments, material_table_name):
    """检查材料在图片和文本中的存在情况（按表找图）"""
    comment_issues = []

    bracketFlag=False
    for process_name, material_names in process_materail_relations:
        print("工艺原料:",process_name,material_names)
        if(has_brackets(process_name)):
            bracketFlag=True
            break
        for material_name in material_names:
            if(has_brackets(material_name)):
                bracketFlag=True
                break
    
    
    if bracketFlag:
        print("包含括号，正在使用括号匹配...")
        comment_issue0=(material_table_name,"请勿在原辅料情况表中使用括号！")
        comment_issues.append(comment_issue0)


    for i, (process, matierals) in enumerate(process_materail_relations):
        # 检查材料在图片中的情况

        graph_name=find_graph_name_by_keywords(relations,[process])
        print("工艺流程图名：",graph_name)

        matierials_related_graph = []
        from SearchContent.test_substring import find_in_second_not_in_first
        
        # 将OCRResult对象转换为字符串列表
        img_text_strings = []
        for ocr_result in related_img_texts[i]:
            if hasattr(ocr_result, 'text'):
                img_text_strings.append(ocr_result.text)
            else:
                img_text_strings.append(str(ocr_result))
        
        matierials_related_graph = find_in_second_not_in_first(img_text_strings, matierals)
        print(i, "原料相关图片：", matierials_related_graph)

        # 检查材料在文本中的情况
        matierials_related_text = []
        for matieral in matierals:
            if search_content_in_text(realted_text_segments[i], matieral):
                matierials_related_text.append({"name": matieral, "related": True})
            else:
                matierials_related_text.append({"name": matieral, "related": False})
        print(i, "原料相关文本：", matierials_related_text)

        # 生成图片相关的注释
        if len(matierials_related_graph) != 0:
            comment_word_graph = process + "工艺中，原辅料表中原材料在对应工艺流程说明图中未找到！" + "原材料缺失："
            for matieral_related in matierials_related_graph:
                comment_word_graph = comment_word_graph + matieral_related + " "
            print(comment_word_graph)
        else:
            comment_word_graph = process + "工艺中，原辅料表中原材料在对应工艺流程说明图中均已找到！"

        # 生成文本相关的注释
        comment_word_text = process + "工艺中，原辅料表中原材料在对应工艺流程说明文本中未找到！" + "原材料缺失："
        markflag = True
        for matieral_related in matierials_related_text:
            if not matieral_related["related"]:
                comment_word_text = comment_word_text + matieral_related["name"] + " "
                markflag = False
        if markflag:
            comment_word_text = process + "工艺中，原辅料表中原材料在对应工艺流程说明文本中均已找到！"
        print(comment_word_text)

        # 添加注释问题
        #comment_issue1 = (material_table_name, comment_word_graph)
       # comment_issue2 = (material_table_name, comment_word_text)
        comment_issue1 = (graph_name, comment_word_graph)
        comment_issue2 = (graph_name, comment_word_text)
        comment_issues.append(comment_issue1)
        comment_issues.append(comment_issue2)

    return comment_issues

def replace_symbols(strings):
    """
    将字符串数组中的顿号（、）和斜杠（/ 和 \）替换为空格

    参数:
        strings: list[str] 或 list[OCRResult], 待处理的字符串数组或OCR结果对象数组

    返回:
        list[str]: 处理后的字符串数组，其中顿号和斜杠已被替换为空格
    """
    # 创建字符映射表：顿号、正斜杠、反斜杠 → 空格
    trans_table = str.maketrans({
        '、': ' ',  # 中文顿号
        '/': ' ',   # 正斜杠
        '\\': ' '   # 反斜杠（需双写转义）
    })

    # 应用映射表到每个字符串
    processed_strings = []
    for s in strings:
        if hasattr(s, 'text'):
            # 处理OCRResult对象
            processed_strings.append(s.text.translate(trans_table))
        else:
            # 处理普通字符串
            processed_strings.append(s.translate(trans_table))
    
    return processed_strings

def find_text_by_keywords(texts, keywords):
    """
    使用多种方法查找包含所有关键词的文本

    参数:
        texts: 待搜索的文本列表
        keywords: 关键词列表，如["干法人造石板材", "生产工艺流程", "图"]

    返回:
        包含所有关键词的文本片段（如果存在）
    """
    for text in texts:
        if all(keyword in text for keyword in keywords):
            return text
    return None

def find_graph_name_by_keywords(relations, keywords):
    """
    在relations中的文本或文本框中根据关键词寻找graph_name并返回

    参数:
        relations: list, 包含表格和图片关系的数据列表
        keywords: list, 关键词列表，用于匹配graph_name

    返回:
        str: 找到的graph_name，如果未找到则返回None
    """
    print("查找图名关键字：",keywords)

    for rel in relations:
        # 检查cell_text中的关键词
        cell_text_content = rel.get('cell_text', '')
        cell_texts=cell_text_content.split('\n')
        for cell_text in cell_texts:
            for keyword in keywords:
                if keyword in cell_text:
                    # 提取可能的graph_name
                    # 假设graph_name格式为"XXX生产工艺流程"或"XXX工艺流程"
                    if "工艺流程图" in cell_text:
                        return cell_text
                  
        # 检查text_parts中的关键词
        text_parts = rel.get('text_parts', [])
        for text_part in text_parts:
            for keyword in keywords:
                if keyword in text_part:
                    # 提取可能的graph_name
                    if "工艺流程图" in text_part:
                        return text_part
        
        cell_text_content = rel.get('cell_text', '')
        cell_texts=cell_text_content.split('\n')
        for cell_text in cell_texts:
            for keyword in keywords:
                if keyword in cell_text:
                    # 提取可能的graph_name
                    # 假设graph_name格式为"XXX生产工艺流程"或"XXX工艺流程"
                    import re
                    patterns = [
                        r'^(.+)工艺流程图$',
                        r'(.+?工艺.+?图)',
                        r'(.+?工艺流程.+?图)'

                    ]
                    for pattern in patterns:
                        matches = re.findall(pattern, cell_text)
                        if matches:
                            print("文字匹配为：",pattern)
                            return cell_text
        
        # 检查text_parts中的关键词
        text_parts = rel.get('text_parts', [])
        for text_part in text_parts:
            for keyword in keywords:
                if keyword in text_part:
                    # 提取可能的graph_name
                    import re
                    patterns = [
                         r'^(.+)工艺流程图$',
                         r'(.+?工艺.+?图)',
                        r'(.+?工艺流程.+?图)'
 
                    ]
                    for pattern in patterns:
                        matches = re.findall(pattern, text_part)
                        if matches:
                            print("文本框匹配为：",pattern)
                            return text_part
    
    return None

def check_images_in_materials_table(process_materail_relations, related_img_texts, related_texts_content, related_text_parts,material_table_name='主要原辅料情况表'):
    """检查图片中的材料在材料表中的存在情况（按图找表）"""
    comment_issues = []

    for i, (process, matierials) in enumerate(process_materail_relations):
        # 替换符号
        related_img_text = replace_symbols(related_img_texts[i])
        matierials = replace_symbols(matierials)

        # 导入必要的函数
        from SearchContent.test_find_missing_strings import find_missing_strings, filter_partial_matches, final_filter_strings

        # 使用test_find_missing_strings中的函数处理
        missing_strings, _ = find_missing_strings(related_img_text, matierials)
        print(i, "图片中未在材料表中出现的内容(初步):", missing_strings)

        # 过滤部分匹配项
        missing_strings = filter_partial_matches(missing_strings, matierials)
        print(i, "图片中未在材料表中出现的内容(过滤部分匹配后):", missing_strings)

        # 最终过滤（去除含"原料"、"成品"等关键词的字符串）
        missing_strings = final_filter_strings(missing_strings)
        print(i, "图片中未在材料表中出现的内容(最终结果):", missing_strings)

        # 保持原有变量名以兼容后续代码
        graph_related_matierials = missing_strings
        print(i, "图片相关原料：", graph_related_matierials)

        # 生成注释内容
        if len(graph_related_matierials) != 0:
            comment_word_graph = process + "工艺中，工艺流程图中原材料在对应原辅料表中未找到！" + "原材料缺失："
            for matieral_related in graph_related_matierials:
                comment_word_graph = comment_word_graph + matieral_related + " "
            print(comment_word_graph)
        else:
            comment_word_graph = process + "工艺中，工艺流程图中原材料在对应原辅料表中均已找到！"

        # 查找注释位置
        related_text_list = related_texts_content.split("\n")
        comment_position = find_text_by_keywords(related_text_list, [process, "工艺流程图"])
        if comment_position is None:
            comment_position = find_text_by_keywords(related_text_parts, [process, "工艺流程图"])
        if comment_position is None:
            comment_position = find_text_by_keywords(related_text_list, [process, "工艺", "图"])
        if comment_position is None:
            comment_position = find_text_by_keywords(related_text_parts, [process, "工艺", "图"])

        comment_position=material_table_name
        print("comment_position:", comment_position)

        comment_issue3 = (comment_position, comment_word_graph)
        comment_issues.append(comment_issue3)

    return comment_issues

def check_equipment_in_related_text(doc_bytes,related_text_content):
      #生产设备查找
    comment_issues=[]
    extracted_datas=extract_named_table(doc_bytes, "建设项目主要生产设备一览表")
    equipments=[]
    for i,extracted_data in enumerate(extracted_datas):
        if i!=0:
            equipments.append(extracted_data[4])
    print("生产设备：",equipments)
    missing_equipments=[equipment for equipment in equipments if equipment not in related_text_content]
    print("文本中未出现的生产设备：",missing_equipments)
    comment_position="建设项目主要生产设备一览表"
    comment_content="文本中未出现的生产设备："+" ".join(missing_equipments)
    comment_issues.append((comment_position,comment_content))
    return comment_issues

def save_document_with_comments(doc_bytes, comment_issues):
    """保存带有注释的文档"""
    from SearchContent.add_comment import add_single_comment
    from spire.doc import Document as SpireDoc
    from spire.doc.common import Stream
    from spire.doc import FileFormat

    doc = SpireDoc()
    doc.LoadFromStream(Stream(doc_bytes), FileFormat.Docx)

    # 添加所有注释
    for comment_issue in comment_issues:
        add_single_comment(doc, "表格插图一致性审核",comment_issue[0], comment_issue[1])

    # 保存文档
    import time
    current_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
    doc.SaveToFile("output/comment-" + current_time + ".docx")

    # 转换为字节数组
    output_stream = Stream()
    doc.SaveToStream(output_stream, FileFormat.Docx)
    word_bytes = output_stream.ToArray()
    doc.Close()

    return word_bytes

if __name__ == "__main__":
    #doc_path=r"C:\Users\<USER>\Desktop\test-doc\sample2.docx"

    material_text="陶粒/石子/再生集料（建筑垃圾破碎料、矿山尾矿渣、钢渣、陶粒、煤矸 石、炉渣等）"
    print(has_brackets(material_text))

    
    
    doc_path=r"C:\Users\<USER>\Desktop\test-doc\sample6.docx"
    with open(doc_path, "rb") as file:
        word_bytes = file.read()
    add_comment_on_matrial(word_bytes)
    