import streamlit as st
from shared.audit_page import AuditPage  # 替换为你实际的路径
from plugins.pdf import PdfProcessor

# 插件初始化函数
def init_pdf_processor():
    """初始化PDF处理器
    
    功能：
    - 设置PDF文件上传界面
    - 准备PDF一致性审核所需参数
    
    参数：无
    返回值：无
    """
    st.markdown("📎 上传多个 PDF 文件用于一致性审核")
    
    uploaded_files = st.file_uploader(
        "上传 PDF 文件（可多选）", type=["pdf"], accept_multiple_files=True
    )

    if not uploaded_files:
        st.warning("请上传至少一个 PDF 文件。")
        st.stop()

    pdf_bytes = [file.read() for file in uploaded_files]
    return PdfProcessor(pdf_bytes=pdf_bytes)

# 页面入口
if __name__ == "__main__":
    page = AuditPage(init_pdf_processor,'附件一致性审核')
    page.render()
