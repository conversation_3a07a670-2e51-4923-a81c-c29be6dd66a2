from io import BytesIO
from typing import Iterable, Tuple, Union, List, Dict
from docx import Document
from docx.text.run import Run
from docx.text.paragraph import Paragraph

class CustomDocument:
    """
    自定义文档类，用于处理 Word 文档的文本和批注。
    """
    def __init__(self, docx_bytes: bytes):
        """
        初始化 CustomDocument 实例。

        参数:
            docx_bytes (bytes): Word 文档的字节数据。
        """
        self._doc = Document(BytesIO(docx_bytes))
        self._text_positions: List[Tuple[Paragraph, Run, int]] = []  # (para, run, start_pos)
        self._run_index: Dict[Run, int] = {}  # run -> index
        self._reindex()

    def _reindex(self):
        """
        重建文本位置索引，不修改文档内容。
        遍历文档中的所有段落、表格和嵌套表格，记录每个 run 的位置信息。
        """
        self._text_positions.clear()
        self._run_index.clear()
        pos, idx = 0, 0
        for para in self._doc.paragraphs:
            for run in para.runs:
                self._text_positions.append((para, run, pos))
                self._run_index[run] = idx
                idx += 1
                pos += len(run.text)

        def process_table(table, start_pos: int, start_idx: int) -> Tuple[int, int]:
            """
            处理表格内容，递归处理嵌套表格。

            参数:
                table: 要处理的表格对象。
                start_pos (int): 当前文本位置。
                start_idx (int): 当前 run 索引。

            返回:
                Tuple[int, int]: 新的文本位置和 run 索引。
            """
            pos, idx = start_pos, start_idx
            for row in table.rows:
                try:
                    cells = row.cells
                except IndexError:
                    print(f"警告：在表格处理中因 IndexError 跳过一行")
                    continue
                for cell in cells:
                    for para in cell.paragraphs:
                        for run in para.runs:
                            self._text_positions.append((para, run, pos))
                            self._run_index[run] = idx
                            idx += 1
                            pos += len(run.text)
                    for nested_table in cell.tables:
                        pos, idx = process_table(nested_table, pos, idx)
            return pos, idx

        for table in self._doc.tables:
            pos, idx = process_table(table, pos, idx)

    def get_marked_text(self) -> str:
        """
        获取带有 '@' 分隔符的纯文本，用于定位。

        返回:
            str: 带有 '@' 分隔符的纯文本。
        """
        return ''.join(f'@{run.text}' for _, run, _ in self._text_positions)

    def insert_comment_at_position(
        self,
        positions: Union[Tuple[int, str], Iterable[Tuple[int, str]]],
        author: str = "作者",
        dedup_text: bool = True
    ) -> None:
        """
        在指定位置插入批注，不破坏或新增 run。

        参数:
            positions: (pos, comment) 或 [(pos, comment), ...]，pos 基于 get_marked_text 的位置。
            author (str): 批注作者，默认为 "作者"。
            dedup_text (bool): 如果为 True，只有 (run.text, comment) 首次出现时才插入批注。

        抛出:
            ValueError: 如果位置超出文档范围。
        """
        items: List[Tuple[int, str]] = (
            [positions] if isinstance(positions, tuple) and isinstance(positions[0], int) else list(positions)
        )
        if not items:
            return
        items.sort(key=lambda x: x[0])

        actions: List[Tuple[int, Run, str]] = []
        item_idx, total_items = 0, len(items)
        seen_pairs = set()

        for _, run, start_pos in self._text_positions:
            if item_idx >= total_items:
                break
            run_len = len(run.text)
            marked_start = start_pos + self._run_index[run]

            while item_idx < total_items and marked_start <= items[item_idx][0] <= marked_start + run_len:
                pos, comment = items[item_idx]
                key = (run.text, comment)

                if not dedup_text or key not in seen_pairs:
                    actions.append((pos, run, comment))
                    if dedup_text:
                        seen_pairs.add(key)
                item_idx += 1

        if item_idx < total_items:
            raise ValueError(f"位置 {items[item_idx][0]} 超出文档范围")

        for pos, run, comment in sorted(actions, reverse=True):
            self._doc.add_comment([run], comment, author=author)

    def get_docx_bytes(self) -> bytes:
        """获取文档的字节内容
        
        功能：
        - 将当前文档对象转换为字节流
        - 可用于保存或进一步处理
        
        参数：无
        返回值：
            bytes: 文档的字节内容
        """
        """
        获取修改后的 DOCX 文档字节流。

        返回:
            bytes: 修改后的文档字节数据。
        """
        buf = BytesIO()
        self._doc.save(buf)
        return buf.getvalue()

if __name__ == "__main__":
    docx = Document()
    para = docx.add_paragraph()
    para.add_run("重复文字")
    para.add_run("，用于演示。")
    para = docx.add_paragraph()
    para.add_run("重复文字")  # 同样的文字重复一次
    para.add_run("第二次出现")
    buf = BytesIO()
    docx.save(buf)
    buf.seek(0)

    doc = CustomDocument(buf.getvalue())
    marked = doc.get_marked_text()
    print(marked)  # @重复文字@，用于演示。@重复文字@第二次出现
    doc.insert_comment_at_position((marked.index('第二次'), '跌热词'))
    doc.insert_comment_at_position(
        [(1, "注释A"), (13, "注释A"), (1, "注释B")],
        author="测试者",
        dedup_text=True
    )
    with open("out.docx", "wb") as f:
        f.write(doc.get_docx_bytes())
    print("完成")
