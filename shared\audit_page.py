import streamlit as st
import os
import inspect
from typing import Callable, Optional

class AuditPage:
    """
    通用审核页面框架。
    通过传入参数初始化函数，内部负责文件上传、参数采集、处理及下载。
    """

    def __init__(self, plugin_factory: Callable[[], Callable], title: Optional[str] = None):
        """
        初始化 AuditPage 实例。

        参数:
            plugin_factory (Callable[[], Callable]): 用于创建插件实例的工厂函数。
            title (Optional[str]): 页面标题，默认为当前文件名（不含扩展名）。
        """
        # 页面标题，默认当前文件名（不含扩展名）
        self.title = title or os.path.splitext(os.path.basename(inspect.stack()[1].filename))[0]
        self.plugin_factory = plugin_factory
        self.state_prefix = self.title.replace(" ", "_").lower()
        self.plugin = None

    def _state_key(self, suffix: str) -> str:
        """生成唯一的Streamlit状态键
        
        参数:
            suffix: 键的后缀字符串
            
        返回值:
            str: 格式为"插件名_后缀"的唯一键
        """
        """
        生成唯一的会话状态键。

        参数:
            suffix (str): 键的后缀。

        返回:
            str: 完整的会话状态键。
        """
        return f"{self.state_prefix}_{suffix}"

    def render(self):
        """
        渲染审核页面，包括文件上传、参数设置、处理操作和结果下载。
        """
        st.markdown(f"<h1 style='text-align:center; margin-bottom:30px;'>{self.title}</h1>", unsafe_allow_html=True)

        processed_key = self._state_key("processed")
        filename_key = self._state_key("filename")
        current_page_key = self._state_key("current_page")

        # 检测页面切换并清除文件上传状态
        current_page_id = getattr(st.session_state, 'selected_page', None) or getattr(st.session_state, 'page', None)

        if current_page_key in st.session_state:
            # 如果检测到页面切换，清除当前页面的文件上传相关状态
            if st.session_state[current_page_key] != current_page_id:
                # 清除文件上传器状态
                upload_widget_key = f"{self.title}_upload"
                if upload_widget_key in st.session_state:
                    del st.session_state[upload_widget_key]

                # 清除处理结果状态
                st.session_state[processed_key] = None
                st.session_state[filename_key] = None

        # 更新当前页面标识
        st.session_state[current_page_key] = current_page_id

        # 初始化会话状态
        for key in (processed_key, filename_key):
            if key not in st.session_state:
                st.session_state[key] = None

        # 上传区
        with st.container():
            st.subheader("📤 上传文档")
            uploaded = st.file_uploader("请选择 Word 文档 (.docx)", type=["docx"], key=f"{self.title}_upload", label_visibility="collapsed")

        if uploaded:
            st.divider()
            st.subheader("⚙️ 参数设置")

            # 调用外部函数，内部通过streamlit完成参数UI，返回插件实例
            self.plugin = self.plugin_factory()

            st.subheader("🚀 操作")
            col_run, col_cancel, _ = st.columns([1, 1, 3])
            with col_run:
                run = st.button("开始处理", key=f"{self.title}_run", type="primary", use_container_width=True)
            with col_cancel:
                cancel = st.button("重置", key=f"{self.title}_cancel", use_container_width=True)

            if run:
                with st.spinner("处理中..."):
                    data = uploaded.read()
                    result = self.plugin(data)
                    st.session_state[processed_key] = result
                    st.session_state[filename_key] = uploaded.name.replace(".docx", "_processed.docx")
                st.toast("✅ 处理完成", icon="✅")

            if st.session_state[processed_key]:
                st.divider()
                st.subheader("📥 下载结果")
                st.download_button(
                    label="下载处理后的文档",
                    data=st.session_state[processed_key],
                    file_name=st.session_state[filename_key],
                    mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    key=f"{self.title}_download",
                    type="primary",
                    use_container_width=True,
                )

            if cancel:
                st.session_state[processed_key] = None
                st.session_state[filename_key] = None
                st.toast("已重置", icon="🔄")
                st.experimental_rerun()
        else:
            st.info("👆 请上传 Word 文档开始", icon="ℹ️")
